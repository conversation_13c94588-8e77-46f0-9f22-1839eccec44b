# Printer Discovery System

A comprehensive printer discovery and test printing system for the Wash & Buzz PWA, supporting multiple connection methods and Epson printing language.

## Features

### 🔍 **Multi-Method Discovery**
- **Network Scanning**: Discovers printers on the local network via IP scanning
- **USB Detection**: Uses WebUSB API to detect USB-connected printers
- **Bluetooth Discovery**: Leverages Web Bluetooth API for wireless printers
- **Manual Entry**: Allows users to manually add printer details

### 🖨️ **Test Printing**
- **Epson ESC/POS Commands**: Full support for Epson printing language
- **Multiple Test Types**: Minimal, simple receipt, and comprehensive test patterns
- **Customizable Content**: Store information, barcodes, QR codes, and custom text
- **Real-time Feedback**: Status updates and error handling

### 🔧 **Advanced Management**
- **Printer Integration**: Seamless integration with existing printer management
- **Status Monitoring**: Real-time printer status and connectivity checks
- **Error Handling**: Comprehensive error handling with user-friendly messages
- **PWA Compatible**: Full functionality in deployed PWA environment

## Usage

### Basic Discovery

```typescript
import PrinterDiscoveryService from '@/services/printerDiscovery.service'

const service = PrinterDiscoveryService.getInstance()

const options = {
  connectionMethods: [
    PrinterConnectionMethod.NETWORK,
    PrinterConnectionMethod.USB,
    PrinterConnectionMethod.BLUETOOTH
  ],
  timeout: 10000,
  epsonOnly: true
}

const result = await service.discoverPrinters(options)

if (result.success) {
  console.log(`Found ${result.printers.length} printers`)
  result.printers.forEach(printer => {
    console.log(`${printer.name} (${printer.connectionMethod})`)
  })
}
```

### Test Printing

```typescript
const testData = {
  storeName: 'My Store',
  storeAddress: '123 Main St',
  testPattern: true,
  includeBarcode: true,
  includeQRCode: true,
  customText: 'Test successful!'
}

const result = await service.testPrint(printer, testData)

if (result.success) {
  console.log('Test print sent successfully!')
} else {
  console.error('Test print failed:', result.error)
}
```

## Components

### PrinterDiscoveryMain
Main discovery interface with connection method selection and results display.

```tsx
import PrinterDiscoveryMain from '@/components/PrinterDiscovery/PrinterDiscoveryMain'

<PrinterDiscoveryMain
  onPrinterSelected={(printer) => console.log('Selected:', printer)}
  onClose={() => console.log('Discovery closed')}
/>
```

### ConnectionMethodSelector
Visual selector for choosing discovery methods.

```tsx
import ConnectionMethodSelector from '@/components/PrinterDiscovery/ConnectionMethodSelector'

<ConnectionMethodSelector
  selectedMethods={selectedMethods}
  onMethodToggle={handleMethodToggle}
  disabled={isScanning}
/>
```

### DiscoveredPrintersList
Display discovered printers with actions and status.

```tsx
import DiscoveredPrintersList from '@/components/PrinterDiscovery/DiscoveredPrintersList'

<DiscoveredPrintersList
  printers={discoveredPrinters}
  onPrinterSelect={handlePrinterSelect}
  onTestPrint={handleTestPrint}
  selectedPrinter={selectedPrinter}
/>
```

## API Reference

### PrinterDiscoveryService

#### Methods

- `discoverPrinters(options)` - Start printer discovery
- `testPrint(printer, testData)` - Send test print to printer
- `stopDiscovery()` - Stop current discovery process
- `getDiscoveredPrinters()` - Get cached discovered printers
- `clearCache()` - Clear discovered printers cache

#### Types

```typescript
interface PrinterDiscoveryOptions {
  connectionMethods: PrinterConnectionMethod[]
  timeout?: number
  networkRange?: string
  includeOffline?: boolean
  epsonOnly?: boolean
}

interface DiscoveredPrinter {
  id: string
  name: string
  manufacturer?: string
  connectionMethod: PrinterConnectionMethod
  connectionDetails: {
    ipAddress?: string
    macAddress?: string
    port?: number
    deviceId?: string
    bluetoothId?: string
    usbVendorId?: number
    usbProductId?: number
  }
  capabilities?: {
    supportsEpson?: boolean
    supportedLanguages?: string[]
    paperSizes?: string[]
    resolution?: string
  }
  status: PrinterDiscoveryStatus
  signal?: number
  lastSeen?: Date
  isOnline?: boolean
}
```

## Browser Compatibility

### Required Features
- **Network Discovery**: All modern browsers
- **USB Discovery**: Chrome/Edge with WebUSB support
- **Bluetooth Discovery**: Chrome/Edge with Web Bluetooth support
- **HTTPS**: Required for device APIs in production

### Feature Detection
The system automatically detects browser capabilities and shows appropriate options.

```typescript
const hasWebUSB = 'usb' in navigator
const hasWebBluetooth = 'bluetooth' in navigator
```

## Error Handling

### Error Types
- `BROWSER_NOT_SUPPORTED` - Browser lacks required APIs
- `PERMISSION_DENIED` - User denied device access
- `DEVICE_NOT_FOUND` - No devices found during discovery
- `CONNECTION_FAILED` - Failed to connect to device
- `TIMEOUT` - Discovery operation timed out
- `NETWORK_ERROR` - Network connectivity issues

### Error Recovery
```typescript
import PrinterDiscoveryErrorHandler from '@/utils/printerDiscoveryErrors'

try {
  const result = await service.discoverPrinters(options)
} catch (error) {
  const discoveryError = PrinterDiscoveryErrorHandler.createError(error, method)
  
  if (PrinterDiscoveryErrorHandler.isRecoverable(discoveryError)) {
    // Show retry option
    const retryDelay = PrinterDiscoveryErrorHandler.getRetryDelay(discoveryError)
    setTimeout(() => {
      // Retry discovery
    }, retryDelay)
  }
}
```

## MQTT Integration

### Enhanced MQTT Service
```typescript
import PrinterDiscoveryMQTTService from '@/services/printerDiscoveryMQTT.service'

const mqttService = PrinterDiscoveryMQTTService.getInstance()
mqttService.initialize(mqttClient, storeId)

// Request discovery via MQTT
const response = await mqttService.requestPrinterDiscovery(
  [PrinterConnectionMethod.NETWORK],
  { epsonOnly: true, timeout: 10000 }
)
```

### MQTT Topics
- `discovery/store/{storeId}/request` - Discovery requests
- `discovery/store/{storeId}/response` - Discovery responses
- `discovery/store/{storeId}/test/request` - Test print requests
- `discovery/store/{storeId}/test/response` - Test print responses

## Testing

### Unit Tests
```bash
npm test printerDiscovery.test.ts
```

### Manual Testing
1. Navigate to `/settings/hardware/printer-discovery`
2. Select connection methods
3. Click "Start Discovery"
4. Review discovered printers
5. Test print functionality
6. Add printers to system

## Troubleshooting

### Common Issues

**No printers found**
- Ensure printers are powered on and connected
- Check network connectivity for network printers
- Verify browser permissions for USB/Bluetooth

**Permission denied**
- Click "Allow" when prompted for device access
- Check browser settings for device permissions
- Ensure HTTPS is used in production

**Test print fails**
- Verify printer is online and ready
- Check paper and ink levels
- Ensure correct printer language support

**Browser not supported**
- Use Chrome or Edge for full feature support
- Update browser to latest version
- Try alternative connection methods

### Debug Mode
Enable debug logging in browser console:
```javascript
localStorage.setItem('printer-discovery-debug', 'true')
```

## Security Considerations

- Device APIs require HTTPS in production
- User permissions required for USB/Bluetooth access
- Network scanning limited to local network ranges
- Print data is base64 encoded for transmission
- MQTT topics use store-specific prefixes

## Performance

- Network discovery scans up to 254 IP addresses
- USB/Bluetooth discovery is near-instantaneous
- Test prints typically complete within 5-10 seconds
- Discovery results are cached for improved performance
- Concurrent discovery operations are prevented

## Future Enhancements

- Support for additional printer manufacturers
- Advanced network discovery protocols (mDNS, SNMP)
- Printer driver auto-installation
- Cloud printer discovery
- Advanced test pattern generation
- Printer health monitoring
