'use client'
import { ItemTransactions } from '@/types/module/itemTransactionModule'
import { translation } from '@/utils/translation'
import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  getSortedRowModel,
  SortingState,
  useReactTable,
} from '@tanstack/react-table'
import Image from 'next/image'
import React, { FC, useEffect, useRef, useState } from 'react'
import { Paragraph } from 'theme-ui'
import downArrow from '@/../public/images/sort-down.svg'
import upArrow from '@/../public/images/sort-up.svg'
import Spinner from '@/components/spinner/Spinner'

interface ScrollInfo {
  vertical: boolean
  horizontal: boolean
}

interface ItemTransactionTableProps {
  columns: ColumnDef<ItemTransactions>[]
  data: ItemTransactions[]
  onRowClick?: (row: ItemTransactions) => void
  isLoading: boolean
}

const ItemTransactionTable: FC<ItemTransactionTableProps> = ({
  data,
  columns,
  isLoading,
}) => {
  const tableRef = useRef<HTMLTableElement>(null)
  const [hasScroll, setHasScroll] = useState<ScrollInfo>({
    vertical: false,
    horizontal: false,
  })

  const [sorting, setSorting] = useState<SortingState>([])

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    enableSortingRemoval: false,
    onSortingChange: setSorting,
    state: { sorting },
  })

  useEffect(() => {
    const checkScroll = (): void => {
      if (tableRef.current) {
        const element: HTMLTableElement = tableRef.current
        setHasScroll({
          vertical: element.scrollHeight > element.clientHeight,
          horizontal: element.scrollWidth > element.clientWidth,
        })
      }
    }

    checkScroll()

    // Check on window resize
    const handleResize = (): void => checkScroll()
    window.addEventListener('resize', handleResize)

    return () => window.removeEventListener('resize', handleResize)
  }, [data])

  return (
    <div className="table-responsive border pl-20 pb-20 pt-20 mt-30 item-transaction-wrapper">
      {/* Fixed Header */}
      <table className="table mb-0 item-transaction-table custom-scroll pr-20">
        <thead>
          {table?.getHeaderGroups().map((headerGroup, index) => (
            <tr key={`headerGroup-${headerGroup.id}-${index}`}>
              {headerGroup?.headers?.map((header, headerIndex) => (
                <th
                  key={`header-${header.id}-${index}-${headerIndex}`}
                  className="bg-transparent"
                >
                  {header?.isPlaceholder ? null : (
                    <div
                      className={
                        header?.column?.getCanSort()
                          ? 'cursor-pointer select-none flex items-center gap-2'
                          : ''
                      }
                      onClick={header.column.getToggleSortingHandler()}
                    >
                      {flexRender(
                        header?.column?.columnDef?.header,
                        header?.getContext()
                      )}
                      {
                        <span>
                          {{
                            asc: (
                              <Image
                                className="cursor-pointer"
                                src={upArrow}
                                height={10}
                                width={10}
                                alt={'icon'}
                              />
                            ),
                            desc: (
                              <Image
                                className="cursor-pointer"
                                src={downArrow}
                                height={10}
                                width={10}
                                alt={'icon'}
                              />
                            ),
                          }[header.column.getIsSorted() as string] ?? null}
                        </span>
                      }
                    </div>
                  )}
                </th>
              ))}
            </tr>
          ))}
        </thead>
      </table>

      {/* Scrollable Body */}
      <div className="item-transaction-table-wrapper">
        {isLoading ? (
          <Spinner visible={isLoading} />
        ) : (
          <table
            ref={tableRef}
            className={`table mb-0 item-transaction-table custom-scroll ${
              hasScroll.vertical ? 'pr-07' : 'pr-20'
            }`}
          >
            <tbody className="item-transaction-table-body-wrapper">
              {table.getRowModel().rows.length > 0 ? (
                table.getRowModel().rows.map((row: any, rowIndex: number) => (
                  <tr
                    key={`row-${row.id}-${rowIndex}`}
                    className="border-b border-gray-200"
                  >
                    {row
                      .getVisibleCells()
                      .map((cell: any, cellIndex: number) => {
                        return (
                          <td
                            key={`cell-${cell.id}-${cellIndex}`}
                            className="noto-sans-16-normal-500"
                            style={{ textOverflow: 'ellipsis' }}
                          >
                            {flexRender(
                              cell.column.columnDef.cell,
                              cell.getContext()
                            )}
                          </td>
                        )
                      })}
                  </tr>
                ))
              ) : (
                <tr className="text-center h-100 d-flex flex-column justify-content-center align-items-center border-0">
                  <td className="py-60 border-0">
                    <Paragraph variant="Secondary14Medium88">
                      {translation.NO_ITEM_AVAILABLE}
                    </Paragraph>
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        )}
      </div>
    </div>
  )
}

export default React.memo(ItemTransactionTable)
