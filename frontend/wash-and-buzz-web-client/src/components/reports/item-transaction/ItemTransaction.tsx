'use client'
import { translation } from '@/utils/translation'
import React, { useEffect, useMemo, useState } from 'react'
import { Box, Text } from 'theme-ui'
import SalesAnalysisDateRange from '@/components/reports/sales-analysis/SalesAnalysisDateRange'
import ItemTransactionTable from '@/components/reports/item-transaction/ItemTransactionTable'
import { getSalesReportsData } from '@/store/actions/salesReports.action'
import { useDispatch } from 'react-redux'
import {
  ItemTransactions,
  ItemTransactionsTableColumn,
} from '@/types/module/itemTransactionModule'
import { ColumnDef } from '@tanstack/react-table'
import { CURRENCY_FORMATTER } from '@/utils/currency'
import { useSelector } from 'react-redux'
import { MainStoreType } from '@/types/store/reducers/main.reducers'

const ItemTransaction = () => {
  const dispatch = useDispatch()
  const itemTransactionData = useSelector(
    (state: MainStoreType) => state?.salesReportsData
  )

  const itemTransactionTableColumns: ColumnDef<ItemTransactions>[] = useMemo(
    () => [
      {
        header: ItemTransactionsTableColumn.ITEM,
        accessorKey: 'name',
        enableSorting: false,
        cell: (column) => {
          return column.getValue()
        },
      },
      {
        header: ItemTransactionsTableColumn.BASE_PRICE,
        accessorKey: 'price',
        enableSorting: false,
        cell: (column) => {
          return CURRENCY_FORMATTER.format((column.getValue() as number) || 0)
        },
      },
      {
        header: ItemTransactionsTableColumn.QTY,
        accessorKey: 'quantity',
        enableSorting: false,
        cell: (column) => {
          return column.getValue()
        },
      },
      {
        header: ItemTransactionsTableColumn.SURCHARGE,
        accessorKey: 'surcharge',
        enableSorting: false,
        cell: (column) => {
          return CURRENCY_FORMATTER.format((column.getValue() as number) || 0)
        },
      },
      {
        header: ItemTransactionsTableColumn.DISCOUNT,
        accessorKey: 'discount',
        enableSorting: false,
        cell: (column) => {
          return CURRENCY_FORMATTER.format((column.getValue() as number) || 0)
        },
      },
      {
        header: ItemTransactionsTableColumn.TOTAL,
        accessorKey: 'total',
        enableSorting: true,
        cell: (column) => {
          return CURRENCY_FORMATTER.format((column.getValue() as number) || 0)
        },
      },
    ],
    // eslint-disable-next-line
    [itemTransactionData]
  )

  // Pre-sort data by name with numbers first, then alphabets
  const sortedData = useMemo(() => {
    if (!itemTransactionData?.data?.itemTransaction) return []

    const data = [
      ...(itemTransactionData.data.itemTransaction as ItemTransactions[]),
    ]
    return data.sort((a, b) => {
      const nameA = a.name?.toString() || ''
      const nameB = b.name?.toString() || ''

      // Check if strings start with numbers
      const aStartsWithNumber = /^\d/.test(nameA)
      const bStartsWithNumber = /^\d/.test(nameB)

      // If one starts with number and other doesn't, prioritize the number
      if (aStartsWithNumber && !bStartsWithNumber) return -1
      if (!aStartsWithNumber && bStartsWithNumber) return 1

      // If both start with numbers or both start with letters, sort normally
      return nameA.localeCompare(nameB, undefined, {
        numeric: true,
        sensitivity: 'base',
      })
    })
  }, [itemTransactionData?.data?.itemTransaction])

  useEffect(() => {
    dispatch(getSalesReportsData())
  }, [])

  const [isLoading, setIsLoading] = useState<boolean>(true)
  useEffect(() => {
    if (itemTransactionData.data?.itemTransaction) {
      setIsLoading(false)
    } else {
      setIsLoading(true)
    }
  }, [itemTransactionData.data?.itemTransaction])

  return (
    <>
      <div className="row g-0">
        <Box className={`py-30 px-20 custom-scroll col-12`}>
          <Text as="p" pb="30px" variant="Primary24Demi28">
            {translation.ITEM_TRANSACTION_REPORT}
          </Text>
          <SalesAnalysisDateRange />
          <ItemTransactionTable
            data={sortedData} // Use pre-sorted data
            columns={itemTransactionTableColumns}
            {...{ isLoading }}
          />
        </Box>
      </div>
    </>
  )
}

export default ItemTransaction
