'use client'
import swapIcon from '@/../../public/images/swap-arrow.svg'
import { TicketAlreadyAssignedModal } from '@/components/core/PopupModals/TicketAlreadyAssignedModal'
import {
  assignRackDataAction,
  checkDataValidation,
} from '@/store/actions/assignRack.action'
import { updateCustomerTicketsData } from '@/store/actions/customerTickets.action'
import { AssignRackPayload } from '@/types/module/assignRackModule'
import { TicketValidationStatus } from '@/types/module/customerTicketsModule'
import { MainStoreType } from '@/types/store/reducers/main.reducers'
import {
  INVALID_RACK_ERROR,
  INVALID_TICKET_ERROR,
  INVALID_TICKET_STATUS_ERROR,
} from '@/utils/constant'
import {
  getInvalidTicketStatusMessage,
  getTicketInRackMessage,
} from '@/utils/functions'
import { getOnlyDigitsRegex } from '@/utils/regexMatch'
import { formatTicketNumber } from '@/utils/ticket'
import { translation } from '@/utils/translation'
import Image from 'next/image'
import React, { Fragment, useEffect, useMemo, useRef, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { Box, Button, Input, Text } from 'theme-ui'

const focusAndSelectInput = (ref: React.RefObject<HTMLInputElement>) => {
  if (ref.current) {
    const focusInput = () => {
      ref.current?.focus()
      ref.current?.select()
    }
    focusInput()
    requestAnimationFrame(focusInput)
  }
}

function useFocusAndSelect(
  ref: React.RefObject<HTMLInputElement>,
  condition: boolean
) {
  useEffect(() => {
    if (condition) {
      focusAndSelectInput(ref)
    }
  }, [condition, ref])
}

const AssignTicketRack: React.FC = () => {
  const inputRef = useRef<HTMLInputElement | null>(null)
  const rackInputRef = useRef<HTMLInputElement | null>(null)
  const enterPressedRef = useRef(false)
  const teamMembersData = useSelector(
    (state: MainStoreType) => state.teamMemberData
  )
  const storeData = useSelector((state: MainStoreType) => state.storeData)
  const customerTicketData = useSelector(
    (state: MainStoreType) => state.customerTicketsData?.data
  )
  const [errorMessage, setErrorMessage] = useState<{
    ticket: string | null
    rack: string | null
  } | null>(null)
  const [isSwapped, setIsSwapped] = useState(false)
  const [ticketValue, setTicketValue] = useState('')
  const [rackValue, setRackValue] = useState('')
  const [alreadyAssignModal, setAlreadyAssignModal] = useState(false)
  const [assignRack, setAssignRack] = useState('')
  const [isDuplicateTicket, setIsDuplicateTicket] = useState(false)
  const [isTicketFocused, setIsTicketFocused] = useState(false)
  const [focusTrigger, setFocusTrigger] = useState(false)

  const handleKeyDown = (
    e: React.KeyboardEvent<HTMLInputElement>,
    index: number
  ) => {
    if (e.key !== 'Enter') return
    e.preventDefault()
    enterPressedRef.current = true

    const nextFieldRef =
      fields[1].inputRef?.current || fields[1].rackInputRef?.current

    if (index === 0 && nextFieldRef) {
      nextFieldRef.focus()
    } else if (index === 1 && ticketValue && rackValue) {
      handleAssignRack()
    }
  }

  const dispatch = useDispatch()
  const fields = isSwapped
    ? [
        {
          label: translation.RACK_IDENTIFIER,
          value: rackValue,
          onChange: (e: { target: { value: React.SetStateAction<string> } }) =>
            setRackValue(e.target.value),
          error: errorMessage?.rack,
          rackInputRef,
          onBlur: () => {
            setErrorMessage(null)
          },
        },
        {
          label: translation.TICKET_IDENTIFIER,
          value: isTicketFocused
            ? ticketValue
            : ticketValue
              ? formatTicketNumber(Number(ticketValue), false)
              : '',

          onChange: (e: {
            target: { value: React.SetStateAction<string> }
          }) => {
            const numericValue = e.target.value
              .toString()
              .replace(getOnlyDigitsRegex, '')
            setTicketValue(numericValue)
          },
          onFocus: () => setIsTicketFocused(true),
          onBlur: () => {
            setIsTicketFocused(false)
            if (enterPressedRef.current) {
              enterPressedRef.current = false
            } else {
              setErrorMessage(null)
            }
          },
          error: errorMessage?.ticket,
          inputRef,
        },
      ]
    : [
        {
          label: translation.TICKET_IDENTIFIER,
          value: isTicketFocused
            ? ticketValue
            : ticketValue
              ? formatTicketNumber(Number(ticketValue), false)
              : '',

          onChange: (e: {
            target: { value: React.SetStateAction<string> }
          }) => {
            const numericValue = e.target.value
              .toString()
              .replace(getOnlyDigitsRegex, '')
            setTicketValue(numericValue)
          },
          onFocus: () => setIsTicketFocused(true),
          onBlur: () => {
            setIsTicketFocused(false)
            setErrorMessage(null)
          },
          error: errorMessage?.ticket,
          inputRef,
        },
        {
          label: translation.RACK_IDENTIFIER,
          value: rackValue,
          onChange: (e: { target: { value: React.SetStateAction<string> } }) =>
            setRackValue(e.target.value),
          error: errorMessage?.rack,
          rackInputRef,
          onBlur: () => {
            if (enterPressedRef.current) {
              enterPressedRef.current = false
            } else {
              setErrorMessage(null)
            }
          },
        },
      ]

  const selectedMember = useMemo(() => {
    const selectedMember = teamMembersData.data?.find(
      (member) => member.isSelected
    )
    return selectedMember
  }, [teamMembersData])

  const handleAssignRackSubmit = () => {
    const payload: AssignRackPayload = {
      storeId: storeData.data?.id as number,
      rackId: rackValue,
      ticketIds: [ticketValue],
      teamMemberId: selectedMember?.teamMemberId as string,
      teamMemberName: selectedMember?.name as string,
    }

    setErrorMessage(null)
    dispatch(
      assignRackDataAction(payload, (res: boolean, error?: string) => {
        if (res) {
          dispatch(
            updateCustomerTicketsData([
              {
                ticketId: Number(ticketValue),
                rackId: rackValue,
              },
            ])
          )
          setTicketValue('')
          setErrorMessage(null)
          setAssignRack('')
          setAlreadyAssignModal(false)
          if (!isSwapped) {
            setRackValue('')
            requestAnimationFrame(() => {
              inputRef.current?.focus()
            })
          }
        } else {
          if (error) {
            setErrorMessage((prev) => ({
              ...prev,
              ticket: error?.match(INVALID_TICKET_ERROR)
                ? translation.INVALID_TICKET
                : error?.match(INVALID_TICKET_STATUS_ERROR)
                  ? getInvalidTicketStatusMessage(Number(ticketValue))
                  : null,
              rack: error?.match(INVALID_RACK_ERROR)
                ? getTicketInRackMessage(Number(ticketValue))
                : null,
            }))
          }
        }
      })
    )
  }

  useEffect(() => {
    if (focusTrigger) {
      focusAndSelectInput(inputRef)
      setFocusTrigger(false)
    }
  }, [focusTrigger])

  useEffect(() => {
    inputRef.current?.focus()
  }, [])

  useFocusAndSelect(inputRef, !!errorMessage?.ticket)
  useFocusAndSelect(rackInputRef, !!errorMessage?.rack)

  const handleAssignRack = () => {
    if (!customerTicketData) return

    dispatch(
      checkDataValidation(Number(ticketValue), rackValue, (result) => {
        switch (result.status) {
          case TicketValidationStatus.InvalidTicket:
            setErrorMessage((prev) => ({
              ...prev,
              rack: null,
              ticket: translation.INVALID_TICKET,
            }))
            return
          case TicketValidationStatus.RackInUseByOpenTicket:
            setErrorMessage((prev) => ({
              ...prev,
              ticket: null,
              rack: result.message,
            }))
            return
          case TicketValidationStatus.DuplicateTicketRackAssignment:
            setAlreadyAssignModal(true)
            setAssignRack(result.currentRack)
            setIsDuplicateTicket(true)
            return
          case TicketValidationStatus.AssignedDifferentRack:
            setAlreadyAssignModal(true)
            setAssignRack(result.currentRack)
            return
          case TicketValidationStatus.Valid:
            handleAssignRackSubmit()
            return
          default:
            return
        }
      })
    )
  }
  return (
    <Box
      className="border d-flex justify-content-center flex-column p-3"
      sx={{ width: '195px', borderRadius: '8px', gap: '12px' }}
    >
      {fields?.map((field, index) => (
        <Fragment key={index}>
          <Box className="d-flex flex-column">
            <Text as="label" variant="Primary18Demi111" className="mb-1">
              {field.label}
            </Text>
            <Input
              name={field.label}
              placeholder={field.label}
              value={field.value}
              onChange={field.onChange}
              onFocus={field.onFocus}
              onBlur={field.onBlur}
              className={`${field.error ? 'border-custom-danger' : 'assign-ticket-rack-input'}`}
              ref={field.inputRef || field.rackInputRef}
              onKeyDown={(e) => handleKeyDown(e, index)}
            />
            {field.error && (
              <Text className="font-poppins" as={'p'} sx={{ color: '#fd3a3a' }}>
                {field.error}
              </Text>
            )}
          </Box>
          {index === 0 && (
            <Box
              className="d-flex justify-content-center align-items-center cursor-pointer"
              onClick={() => setIsSwapped(!isSwapped)}
            >
              <Image src={swapIcon} alt="swap-icon" height={20} width={20} />
            </Box>
          )}
        </Fragment>
      ))}

      <Button
        disabled={!ticketValue || !rackValue}
        className="mt-2 font-poppins"
        onClick={() => handleAssignRack()}
      >
        {translation.ASSIGN}
      </Button>

      {alreadyAssignModal && (
        <TicketAlreadyAssignedModal
          isVariantLoading={false}
          isOpen={alreadyAssignModal}
          assignRack={assignRack}
          rackValue={rackValue}
          ticketValue={ticketValue}
          onClose={() => {
            setAlreadyAssignModal(false)
            setAssignRack('')
            if (isDuplicateTicket) {
              setFocusTrigger(true)
            }
            setIsDuplicateTicket(false)
          }}
          handleAssignRackSubmit={handleAssignRackSubmit}
          title={translation?.TICKET_ALREADY_ASSIGNED}
          modalContainer="already-assign-modal"
          isDuplicateTicket={isDuplicateTicket}
        />
      )}
    </Box>
  )
}

export default AssignTicketRack
