'use client'
import DataTable from '@/components/core/Table/ArBillingTable'
import Spinner from '@/components/spinner/Spinner'
import { useCustomerMap } from '@/hooks/useCustomerMap'
import { useHandleClickCustomer } from '@/hooks/useHandleClickCustomer'
import {
  clearAssignRackDataAction,
  getAssignRackDataAction,
} from '@/store/actions/assignRack.action'
import { updateTicketFilter } from '@/store/actions/store.actions'
import {
  getTicketsData,
  resetCustomerData,
} from '@/store/actions/ticket.action'
import {
  AssignRackData,
  AssignRackTableColumn,
} from '@/types/module/assignRackModule'
import { DateRangeSelectionProps } from '@/types/module/dateRangeCalendarModule'
import { Store } from '@/types/module/storeModule'
import { MainStoreType } from '@/types/store/reducers/main.reducers'
import { ASSIGN_RACK_CONTINUATION_TOKEN } from '@/utils/constant'
import { handleDateRangeForPresentDay } from '@/utils/functions'
import { appRoutes } from '@/utils/routes'
import { formatTicketNumber } from '@/utils/ticket'
import { translation } from '@/utils/translation'
import { ColumnDef } from '@tanstack/react-table'
import { format } from 'date-fns'
import Cookies from 'js-cookie'
import { useRouter } from 'next/navigation'
import React, { useCallback, useEffect, useMemo, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { Box } from 'theme-ui'

const AssignRackTable: React.FC = () => {
  const dispatch = useDispatch()
  const storeData = useSelector((state: MainStoreType) => state.storeData.data)
  const assignRackData = useSelector(
    (state: MainStoreType) => state.assignRackData
  )
  const route = useRouter()
  const customers = useSelector((state: MainStoreType) => state.customerData)
  const [isLoading, setIsLoading] = useState(false)
  const [selectedDateRange, setSelectedDateRange] = useState<
    DateRangeSelectionProps[]
  >([])

  const customerMap = useCustomerMap()

  const assignRackTableColumns: ColumnDef<AssignRackData>[] = useMemo(
    () => [
      {
        header: AssignRackTableColumn.Rack,
        accessorKey: 'rackId',
        cell: (column) => {
          if (!column.getValue()) return '-'
          return column.getValue()
        },
      },
      {
        header: AssignRackTableColumn.Ticket,
        accessorKey: 'ticketId',
        cell: (column) => {
          if (!column.getValue()) return '-'
          return formatTicketNumber(column.getValue() as number, false)
        },
      },
      {
        header: AssignRackTableColumn.Assigned,
        accessorKey: 'eventTimestamp',
        cell: (column) => {
          const value = column.getValue() as string
          if (!value) return '-'
          const date = new Date(value)
          return format(date, 'M/dd/yy · h:mm a')
        },
      },

      {
        header: AssignRackTableColumn.Customer,
        accessorKey: 'storeCustomerId',
        cell: (column) => {
          if (column.getValue() === undefined) return '-'
          const customer = customerMap.get(column.getValue() as number)

          return `${customer?.firstName}  ${customer?.lastName}`
        },
      },
      {
        header: AssignRackTableColumn.Phone,
        accessorKey: 'storeCustomerId',
        cell: (column) => {
          if (column.getValue() === undefined) return '-'
          const phone = customerMap.get(
            column.getValue() as number
          )?.phoneNumber

          return phone
        },
      },
    ],
    // eslint-disable-next-line
    [assignRackData.rackAssignments]
  )

  const getAssignRackData = useCallback(
    (storeData: Store, continuationToken?: string | null) => {
      if (storeData) {
        dispatch(
          getAssignRackDataAction(
            { storeId: storeData?.id, continuationToken },
            () => {
              setIsLoading(false)
            }
          )
        )
      }
    },
    [dispatch]
  )

  const onFetchMoreData = () => {
    setIsLoading(false)
    const continuationToken = Cookies.get(ASSIGN_RACK_CONTINUATION_TOKEN)
    const decode = decodeURIComponent(JSON.stringify(continuationToken))
    if (continuationToken && storeData) {
      dispatch(
        getAssignRackDataAction(
          { storeId: storeData?.id, continuationToken: decode },
          () => {
            setIsLoading(false)
          }
        )
      )
    }
  }

  useEffect(() => {
    // side-effect logic here
    if (storeData) {
      setIsLoading(true)
      getAssignRackData(storeData as Store, null)
      Cookies.remove(ASSIGN_RACK_CONTINUATION_TOKEN)

      storeData.ticketSelectedDateRange &&
        setSelectedDateRange(
          handleDateRangeForPresentDay(storeData) as DateRangeSelectionProps[]
        )
    }
    return () => {
      dispatch(clearAssignRackDataAction(null))
    }
  }, [storeData, dispatch, getAssignRackData])

  const [revoke, isRevoke] = useState<boolean>(false)

  const { handleClickCustomer } = useHandleClickCustomer({
    customers,
    shouldRedirect: true,
  })

  const handleTicketClick = useCallback(
    (ticketId: number) => {
      dispatch(
        updateTicketFilter(
          {
            id: storeData?.id,
            ticketSelectedDateRange: [
              {
                startDate: undefined,
                endDate: undefined,
                key: undefined,
              },
            ],
            ticketSelectedDateRangeType: undefined,
            filteredTicketStatus: undefined,
            filteredTicketPaymentStatus: undefined,
            filteredTicketId: ticketId?.toString(),
          },
          (res) => {
            if (res) {
              route.push(appRoutes.tickets)
            }
          }
        )
      )
    },
    [route, storeData, dispatch]
  )

  const getTicketsByDate = useCallback(
    (customerId?: number) => {
      dispatch(resetCustomerData())
      setIsLoading(true)
      dispatch(
        getTicketsData(
          true,
          null,
          customers?.selectedCustomer?.storeCustomerId ?? customerId,
          () => {
            setIsLoading(false)
          }
        )
      )
      Cookies.remove(translation.CONTINUATION_TOKEN)
    },
    // eslint-disable-next-line
    [customers?.selectedCustomer?.storeCustomerId, dispatch, selectedDateRange]
  )

  return (
    <Box
      className="border pl-20 py-20 mr-20"
      sx={{
        flex: 1,
        overflow: 'hidden',
        display: 'flex',
        flexDirection: 'column',
        width: '100%',
        borderRadius: '8px',
      }}
    >
      {assignRackData?.rackAssignments && (
        <DataTable
          data={assignRackData?.rackAssignments as AssignRackData[]}
          columns={assignRackTableColumns}
          className={'assign-rack'}
          hasMoreData={assignRackData?.hasMoreData}
          isLoading={isLoading}
          onFetchMoreData={onFetchMoreData}
          dataLength={assignRackData?.rackAssignments?.length}
          loader={
            assignRackData.loading && (
              <Box sx={{ textAlign: 'center' }}>
                <Spinner visible={assignRackData.loading} />
              </Box>
            )
          }
          isKeepScroll={revoke}
          onDataLoaded={() => isRevoke(false)}
          onTdClick={handleClickCustomer}
          handleTicketClick={handleTicketClick}
          getTicketsByDate={getTicketsByDate}
          emptyMessage={translation.NO_RACK_ASSIGNMENT_AVAILABLE}
          highlightCondition={(row) => row.isRecentlyAdded === true}
        />
      )}
    </Box>
  )
}

export default AssignRackTable
