import { translation } from '@/utils/translation'
import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react'
import { Flex, Text, Divider, Spinner } from 'theme-ui'
import warningIcon from '@/../../public/images/warning-octagon.svg'
import Image from 'next/image'
import { PrintersDataState } from '@/types/store/reducers/printers.reducers'
import { useSelector, useDispatch } from 'react-redux'
import { MainStoreType } from '@/types/store/reducers/main.reducers'
import { PrinterData } from '@/types/module/printersModule'
import { MqttPrinterConnection } from '@/components/selectStore/MqttPrinterConnection'
import { updateSelectedPrinterData } from '@/store/actions/printers.action'
import chevronDown from '@/../public/images/chevron-down-black.svg'
import chevronUp from '@/../public/images/chevron-up-black.svg'
import { showSuccessToast } from '@/components/core/Toast/CustomToast'

const PrinterDropdown: React.FC = () => {
  const dispatch = useDispatch()
  const dropdownToggleRef = useRef<HTMLDivElement>(null)

  // Redux selectors with memoization
  const storeData = useSelector((state: MainStoreType) => state?.storeData)
  const printers: PrintersDataState = useSelector(
    (state: MainStoreType) => state?.printersData
  )

  // State management
  const [toggleSection, setToggleSection] = useState<boolean>(false)
  const [connectedPrinters, setConnectedPrinters] = useState<PrinterData[]>([])
  const [refreshKey, setRefreshKey] = useState(0)
  const [loading, setLoading] = useState(false)
  const [isOpen, setIsOpen] = useState<boolean>(false)

  // Memoized broker URL
  const brokerUrl = useMemo(() => process.env.MQTT_BROKER_URL || '', [])

  // Optimized printer selection handler
  const handlePrinterSelect = useCallback(
    async (selectedPrinter: PrinterData, printers: PrinterData[]) => {
      try {
        const userPreferenceData = JSON.parse(
          localStorage.getItem(translation.USER_PREFERENCE) || '{}'
        )

        const updatedPrinters = printers.map((printer) => ({
          ...printer,
          isSelected:
            printer.id === selectedPrinter.id
              ? !selectedPrinter.isSelected
              : printer.isSelected,
        }))

        const connectedPrinters = updatedPrinters.filter(
          (device) => device.isSelected
        )

        await dispatch(
          updateSelectedPrinterData(connectedPrinters, (res: boolean) => {
            if (res) {
              showSuccessToast(translation.PRINTER_CONNECTED_SUCCESSFULLY)
            }
          })
        )

        // Update localStorage
        userPreferenceData.printers = connectedPrinters.map(
          (printer: PrinterData) => ({
            macAddress: printer.macAddress,
            name: printer.name,
            id: printer.id,
          })
        )

        localStorage.setItem(
          translation.USER_PREFERENCE,
          JSON.stringify(userPreferenceData)
        )
      } catch (error) {
        console.error('Error selecting printer:', error)
      }
    },
    [dispatch]
  )

  // Optimized refresh handler with proper cleanup
  const handleRefresh = useCallback(() => {
    setConnectedPrinters([])
    setRefreshKey((prevKey) => prevKey + 1)
    setLoading(true)

    const timer = setTimeout(() => {
      setLoading(false)
    }, 1000)

    // Return cleanup function properly
    return timer
  }, [])

  // Memoized printer connections to prevent unnecessary re-renders
  const printerConnections = useMemo(() => {
    if (!storeData.data?.id || !printers.data?.length) return null

    return printers.data.map((printer, index) => {
      return (
        <MqttPrinterConnection
          key={`${printer.id}-${refreshKey}`}
          value={printer}
          index={index}
          topicForPrinterStatus={`${translation.STAR_CLOUDPRNT}/${translation.TO_DEVICE}/${printer.macAddress}/${translation.REQUEST_CLIENT_STATUS}`}
          setConnectedPrinters={(value: PrinterData) => {
            setConnectedPrinters((prev) => [...prev, value])
          }}
        />
      )
    })
  }, [storeData.data?.id, printers.data, refreshKey, brokerUrl])

  // Memoized dropdown items
  const dropdownItems = useMemo(() => {
    if (loading) return null

    if (connectedPrinters.length === 0 && !loading) {
      return (
        <Flex
          sx={{ alignItems: 'center', p: '20px' }}
          className="dropdown-item-container"
        >
          <Text
            as="li"
            sx={{ pl: '10px' }}
            className="dropdown-item"
            variant="Primary16Regular20"
          >
            {translation.NO_PRINTER_FOUND}
          </Text>
        </Flex>
      )
    }

    return connectedPrinters.map((item, index: number) => (
      <React.Fragment key={`dropdown-${item.id}`}>
        <Flex
          sx={{ alignItems: 'center', p: '20px' }}
          className="dropdown-item-container"
          onClick={() =>
            handlePrinterSelect(item, printers.data as PrinterData[])
          }
        >
          <Text
            as="li"
            sx={{ pl: '10px' }}
            className="dropdown-item"
            variant="Primary16Regular20"
          >
            {item?.name}
          </Text>
        </Flex>
        {index !== connectedPrinters.length - 1 && <Divider m={0} />}
      </React.Fragment>
    ))
  }, [loading, connectedPrinters, handlePrinterSelect, printers.data])

  // Dropdown event handlers
  useEffect(() => {
    const dropdownToggle = dropdownToggleRef.current

    const handleDropdownOpen = () => {
      setIsOpen(true)
      setToggleSection(true)
    }

    const handleDropdownClose = () => {
      setConnectedPrinters([])
      setIsOpen(false)
      setToggleSection(false)
      setLoading(false)
    }

    if (dropdownToggle) {
      dropdownToggle.addEventListener('show.bs.dropdown', handleDropdownOpen)
      dropdownToggle.addEventListener('hide.bs.dropdown', handleDropdownClose)
    }

    return () => {
      if (dropdownToggle) {
        dropdownToggle.removeEventListener(
          'show.bs.dropdown',
          handleDropdownOpen
        )
        dropdownToggle.removeEventListener(
          'hide.bs.dropdown',
          handleDropdownClose
        )
      }
    }
  }, [])

  // Handle refresh when dropdown opens
  useEffect(() => {
    if (isOpen && printers?.data && printers?.data?.length > 0) {
      const timer = handleRefresh()
      return () => clearTimeout(timer)
    } else {
      return
    }
  }, [isOpen, printers.data?.length, handleRefresh])

  return (
    <section className="printer-home-page custom-scroll">
      <div className="d-flex justify-content-between">
        <div className="dropdown dropup">
          <Flex
            className="user-navbar home-printer-dropdown"
            id="printerDropdownMenuButton"
            data-bs-toggle="dropdown"
            aria-expanded={isOpen}
            ref={dropdownToggleRef}
            role="button"
          >
            <div className="d-flex align-items-center gap-3">
              <div className="text-center printer-connection-wrapper">
                <Image
                  className="warning-img"
                  height={20}
                  width={20}
                  src={warningIcon}
                  alt="warning"
                />
                <Text
                  className="no-printer-connected d-inline-block"
                  variant="Primary18Medium20"
                >
                  {translation.NO_PRINTER_CONNECTED}
                </Text>
              </div>

              <div style={{ height: '30px', width: '30px' }}>
                {!loading ? (
                  <Image
                    className="dropdown-arrow"
                    src={toggleSection ? chevronUp : chevronDown}
                    width={20}
                    height={20}
                    alt="icon"
                  />
                ) : (
                  <Spinner
                    className="printer-loader"
                    width={30}
                    height={30}
                    color="white"
                  />
                )}
              </div>
            </div>
          </Flex>

          {/* MQTT Printer Connections */}
          <div>{printerConnections}</div>

          {/* Dropdown Menu */}
          <Text
            as="ul"
            className="dropdown-menu dropdown-web p-0 dropdown-menu-end"
            aria-labelledby="printerDropdownMenuButton"
          >
            {dropdownItems}
          </Text>
        </div>
      </div>
    </section>
  )
}

export default PrinterDropdown
