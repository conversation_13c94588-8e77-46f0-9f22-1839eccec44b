'use client'
import { ThemeButton } from '@/components/core/Button/Button'
import {
  CustomDivider,
  PopupModalHeaderWithBackButton,
} from '@/components/core/Text/Texts'
import { TextInputField } from '@/components/core/TextInputField/TextInputField'
import { TextCurrency } from '@/components/core/Ticket/TextCurrency'
import Spinner from '@/components/spinner/Spinner'
import { PaymentFieldPlaceholder } from '@/types/module/paymentsModule'
import { PaymentIncentivesType } from '@/types/module/priceIncentiveModule'
import {
  PaidAmountData,
  PaymentMethodType,
} from '@/types/module/ticketNdSalesModule'
import { MainStoreType } from '@/types/store/reducers/main.reducers'
import { EXTERNAL, iframeCss } from '@/utils/constant'
import { CURRENCY_FORMATTER } from '@/utils/currency'
import {
  convertToCurrency,
  encodeCss,
  isEmptyOrTwoDecimal,
  parseCurrencyToNumber,
  removeNonNumericChars,
  roundAmountByTwoDigits,
} from '@/utils/functions'
import {
  validateDecimal,
  zipCodeOnChangeRegex,
  zipCodeOnSubmitRegex,
} from '@/utils/regexMatch'
import { getExpiryDate, getPayAndCheckoutButtonText } from '@/utils/strings'
import { transactionWithFunction, translation } from '@/utils/translation'
import { useFormik } from 'formik'
import React, { useCallback, useEffect, useRef, useState } from 'react'
import { useSelector } from 'react-redux'
import Modal from 'react-responsive-modal'
import { Box, Paragraph, Text } from 'theme-ui'
import * as Yup from 'yup'

interface Field {
  name: string
  label: string
  value: string
  placeholder: string
  autoFocus?: boolean
  ref?: React.RefObject<HTMLInputElement>
  onFocus?: () => void
  onBlur?: (e: React.FocusEvent<HTMLInputElement>) => void
  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void
  isIntegrated?: boolean
}

interface PaymentManualCreditCardProps {
  amount: number
  isOpen: boolean
  onClose: () => void
  onBack: () => void
  closeIcon?: React.ReactNode
  showCloseIcon?: boolean
  onPayment?: (data: PaidAmountData | null, isPayOnly?: boolean) => void
  loading?: boolean
  payableAmount?: number
  updatePayableAmount?: (value: string) => void
  currentPaymentIncentiveName?: PaymentIncentivesType
  isPayAndCheckoutButton?: boolean
}

export const PaymentManualCreditCard: React.FC<
  PaymentManualCreditCardProps
> = ({
  amount,
  isOpen,
  onClose,
  onBack,
  closeIcon,
  showCloseIcon = false,
  onPayment,
  loading = false,
  payableAmount,
  updatePayableAmount,
  currentPaymentIncentiveName,
  isPayAndCheckoutButton = false,
}) => {
  const isIntegrated = useSelector(
    (state: MainStoreType) =>
      state?.storeData?.data?.isPaymentTerminalIntegrated
  )
  const [isIframeLoaded, setIframeLoaded] = useState(false)
  const [isCheckAmountFocused, setIsCheckAmountFocused] = useState(false)
  const [isCardInfoValid, setIsCardInfoValid] = useState(false)
  const [validCardData, setValidCardData] = useState<any>(null)
  const paymentAmountRef = useRef<HTMLInputElement>(null)
  const manualCreditCardSchema = Yup.object({
    paymentAmount: Yup.string()
      .required(translation.PAYMENT_AMOUNT_REQUIRED)
      .test(
        'is-positive',
        `${translation.CREDIT_CARD_AMOUNT_MUST_BE_GREATER_THAN_ZERO}`,
        (value) => (parseCurrencyToNumber(value) ?? 0) > 0
      )
      .test(
        'is-valid-amount',
        transactionWithFunction.amountShouldBeInRange(
          CURRENCY_FORMATTER.format(0),
          CURRENCY_FORMATTER.format(amount)
        ),
        (value) =>
          !(parseCurrencyToNumber(value) ?? 0) ||
          (parseCurrencyToNumber(value) ?? 0) <= amount
      ),
    customerName: Yup.string().required(
      translation.CUSTOMER_NAME_FIELD_REQUIRED
    ),
    zipcode: Yup.string()
      .required(translation.ZIP_CODE_FIELD_REQUIRED)
      .matches(zipCodeOnSubmitRegex, translation.INVALID_ZIP_CODE),
  })

  // Define validation for when isIntegrated is false
  const paymentAmountSchema = Yup.object({
    paymentAmount: Yup.string()
      .required(translation.PAYMENT_AMOUNT_REQUIRED)
      .test(
        'is-valid-amount',
        transactionWithFunction.amountShouldBeInRange(
          0,
          CURRENCY_FORMATTER.format(amount)
        ),
        (value) => !value || parseFloat(value) <= amount
      ),
  })

  const formik = useFormik({
    initialValues: {
      paymentAmount: '',
      customerName: '',
      zipcode: '',
    },
    validationSchema: isIntegrated
      ? manualCreditCardSchema
      : paymentAmountSchema,
    validateOnMount: true,
    onSubmit: () => {},
  })

  const {
    values,
    errors,
    touched,
    setFieldValue,
    handleBlur,
    handleChange,
    isValid,
  } = formik

  const handleIframeLoad = useCallback(() => {
    paymentAmountRef.current?.focus()
    setIframeLoaded(true)
  }, [])

  const handleChangeAmount = useCallback(
    (value: string) => {
      if (validateDecimal.test(value)) {
        setFieldValue('paymentAmount', value)
        updatePayableAmount && updatePayableAmount(value)
      }
      if (value === '') {
        setFieldValue('paymentAmount', '')
        updatePayableAmount && updatePayableAmount('')
      }
    },
    [setFieldValue, updatePayableAmount]
  )

  const handleZipCodeChange = useCallback(
    (value: string) => {
      if (zipCodeOnChangeRegex.test(value)) {
        setFieldValue('zipcode', value)
      }
      if (value === '') {
        setFieldValue('zipcode', '')
      }
    },
    [setFieldValue]
  )

  const isPayCTAEnabled = (): boolean => {
    const inputAmount = parseFloat(
      parseCurrencyToNumber(values?.paymentAmount)?.toString() || '0'
    )
    return inputAmount > 0 && inputAmount <= amount
  }

  const isFormSubmissionReady = () => {
    if (isIntegrated) {
      return isCardInfoValid && isValid && isPayCTAEnabled()
    }
    return isPayCTAEnabled()
  }

  const fields: Field[] = [
    {
      name: translation.PAYMENT_AMOUNT_FIELD_NAME,
      label: translation.PAYMENT_AMOUNT,
      value: isCheckAmountFocused
        ? values.paymentAmount
        : convertToCurrency(values.paymentAmount),
      placeholder: translation.ENTER_AMOUNT,
      autoFocus: true,
      ref: paymentAmountRef,
      onFocus: () => {
        const currentValue = values.paymentAmount
        const numericValue = parseCurrencyToNumber(currentValue)
        setFieldValue(
          'paymentAmount',
          numericValue ? numericValue.toString() : ''
        )
        setIsCheckAmountFocused(true)
      },
      onBlur: (e) => {
        if (isIframeLoaded) {
          handleBlur(e)
          const value = e.target.value
          setFieldValue('paymentAmount', value ? convertToCurrency(value) : '')
          setIsCheckAmountFocused(false)
        }
      },
      onChange: (e) => {
        const value = removeNonNumericChars(e.target.value)
        if (isEmptyOrTwoDecimal(value)) {
          handleChangeAmount(value)
        }
      },
      isIntegrated: true,
    },
    {
      name: translation.CUSTOMER_FIELD_NAME,
      label: translation.PAYMENT_MODAL_CUSTOMER_NAME_LABEL,
      value: values.customerName,
      placeholder: PaymentFieldPlaceholder.EnterCustomerName,
      onChange: handleChange,
      onBlur: (e) => isIframeLoaded && handleBlur(e),
      isIntegrated: false,
    },
    {
      name: translation.ZIPCODE_FIELD_NAME,
      label: translation.ZIPCODE,
      value: values.zipcode,
      placeholder: PaymentFieldPlaceholder.EnterZipCode,
      onChange: (e) => handleZipCodeChange(e.target.value),
      onBlur: (e) => isIframeLoaded && handleBlur(e),
      isIntegrated: false,
    },
  ]

  const getSurcharge = useCallback(
    (fieldName: string) => {
      return (
        payableAmount &&
        `${translation.SURCHARGE?.toLowerCase()} ${translation.EQUAL_SYMBOL} ${CURRENCY_FORMATTER.format(
          roundAmountByTwoDigits(
            payableAmount -
              (parseCurrencyToNumber(
                (values as Record<string, string>)[fieldName]
              ) ?? 0)
          )
        )}`
      )
    },
    [payableAmount, values]
  )

  const renderField = useCallback(
    (field: Field) => (
      <>
        <TextInputField
          key={field.name}
          name={field.name}
          ref={field.ref}
          label={field.label}
          value={(values as Record<string, string>)[field.name]}
          autoFocus={field.autoFocus}
          onFocus={() => {
            field.onFocus?.()
          }}
          onBlur={(e) => {
            field.onBlur?.(e)
          }}
          manualErrorSX={{
            display: 'block',
            textAlign: 'start',
          }}
          onChange={(e) => {
            field.onChange?.(e)
          }}
          errors={(errors as any)[field.name]}
          variant="secondaryInputDemi14"
          placeholder={field.placeholder}
          touched={(touched as Record<string, boolean>)[field.name]}
          wrapperClass="mt-0"
          wrapperSx={{
            marginBottom:
              field.name === translation.PAYMENT_AMOUNT_FIELD_NAME &&
              currentPaymentIncentiveName ===
                PaymentIncentivesType.CREDIT_CARD_SURCHARGE
                ? '3px'
                : '20px',
          }}
          labelVariant="Primary16Medium2"
          labelSx={{ display: 'block', textAlign: 'start' }}
        />
        {field.name === translation.PAYMENT_AMOUNT_FIELD_NAME &&
          currentPaymentIncentiveName ===
            PaymentIncentivesType.CREDIT_CARD_SURCHARGE &&
          (payableAmount || 0) > 0 && (
            <Paragraph
              variant="Primary14Medium12"
              className="text-start"
              mb="10px"
            >
              {(values as Record<string, string>)[field.name] &&
                getSurcharge(field.name)}
            </Paragraph>
          )}
      </>
    ),
    [
      errors,
      touched,
      values,
      payableAmount,
      currentPaymentIncentiveName,
      getSurcharge,
    ]
  )

  const renderIframe = useCallback(
    () => (
      <iframe
        id="tokenFrame"
        name="tokenFrame"
        src={encodeCss(iframeCss)}
        frameBorder="0"
        scrolling="no"
        width="100%"
        height="270px"
        onLoad={handleIframeLoad}
      ></iframe>
    ),
    [handleIframeLoad]
  )

  const getSubmitButtonPrice = useCallback(() => {
    if (!values.paymentAmount)
      return CURRENCY_FORMATTER.format(
        roundAmountByTwoDigits(
          currentPaymentIncentiveName ===
            PaymentIncentivesType.CREDIT_CARD_SURCHARGE
            ? payableAmount || 0
            : amount || 0
        )
      )

    const paymentAmount = parseFloat(values.paymentAmount)
    const adjustedAmount =
      currentPaymentIncentiveName ===
      PaymentIncentivesType.CREDIT_CARD_SURCHARGE
        ? payableAmount || paymentAmount
        : paymentAmount

    return CURRENCY_FORMATTER.format(roundAmountByTwoDigits(adjustedAmount))
  }, [payableAmount, values.paymentAmount, amount, currentPaymentIncentiveName])

  const handleSubmit = useCallback(
    (isPayOnly = false) => {
      if (onPayment) {
        onPayment(
          {
            amount: parseCurrencyToNumber(values.paymentAmount),
            cardHolderName: values.customerName,
            cardHolderZipCode: values.zipcode,
            type: PaymentMethodType.CREDIT_CARD,
            isManualEntry: true,
            ...validCardData,
          },
          isPayOnly
        )
      }
    },
    [onPayment, values, validCardData]
  )

  useEffect(() => {
    const handleMessage = (event: MessageEvent) => {
      try {
        const data = JSON.parse(event.data)
        if (data.token && data.expiry) {
          setIsCardInfoValid(true)
          setValidCardData({
            cardPaymentToken: data.token,
            cardExpiry: getExpiryDate(data.expiry),
          })
        }
      } catch (error) {
        console.error('Error parsing iframe message:', error)
      }
    }

    window.addEventListener('message', handleMessage)
    return () => window.removeEventListener('message', handleMessage)
  }, [])

  useEffect(() => {
    if (!isIntegrated) {
      setValidCardData({
        cardPaymentToken: EXTERNAL,
        cardExpiry: EXTERNAL,
      })
    }
  }, [isIntegrated])

  return (
    <Modal
      open={isOpen}
      onClose={() => {
        if (!loading) {
          onClose()
          formik.resetForm()
        }
      }}
      classNames={{
        modal: 'credit-card-manual-payment-modal py-20 px-40 custom-scroll',
      }}
      center
      closeIcon={closeIcon}
      showCloseIcon={showCloseIcon}
    >
      <PopupModalHeaderWithBackButton
        title={translation.ADD_CREDIT_CARD_INFORMATION}
        onIconClick={onBack}
        src="images/arrow-left.svg"
      />
      <Box
        className={`text-center manual-credit-card-modal my-15 ${isIntegrated ? 'h-670' : ''}`}
      >
        <Text variant="Secondary16Medium125">{translation.TOTAL_AMOUNT}</Text>
        <Paragraph variant="Primary24demi116" mb="10px">
          <TextCurrency amount={amount} />
        </Paragraph>
        <div className="row check-payment-container">
          {(isIntegrated
            ? fields
            : fields.filter((field) => field.isIntegrated)
          ).map(renderField)}
          {isIntegrated && renderIframe()}
        </div>
      </Box>
      <CustomDivider />

      <div
        className={`mt-40 ${isPayAndCheckoutButton ? 'd-flex justify-content-between' : ''}`}
      >
        {isPayAndCheckoutButton && (
          <Box>
            <ThemeButton
              variant="clickableTxtBtn"
              text={translation.PAY_ONLY}
              className="cta-button w-100"
              onClick={() => handleSubmit(true)}
              disabled={!isFormSubmissionReady()}
            />
          </Box>
        )}
        <Box>
          <ThemeButton
            disabled={!isFormSubmissionReady()}
            text={getPayAndCheckoutButtonText(
              isPayAndCheckoutButton,
              getSubmitButtonPrice()
            )}
            className="cta-button ms-auto"
            onClick={() => handleSubmit(false)}
          />
        </Box>
      </div>

      <Spinner visible={loading} />
    </Modal>
  )
}
