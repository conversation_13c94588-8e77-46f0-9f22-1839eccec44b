'use client'
import { ThemeButton } from '@/components/core/Button/Button'
import { PopupModalHeader } from '@/components/core/Text/Texts'
import Spinner from '@/components/spinner/Spinner'
import { TicketAlreadyAssignedModalProps } from '@/types/module/assignRackModule'
import { formatTicketNumber } from '@/utils/ticket'
import { translation } from '@/utils/translation'
import React from 'react'
import Modal from 'react-responsive-modal'
import { Box, Flex, Text } from 'theme-ui'

export const TicketAlreadyAssignedModal: React.FC<
  TicketAlreadyAssignedModalProps
> = ({
  isOpen = false,
  onClose,
  title,
  showCloseIcon = false,
  isVariantLoading,
  assignRack,
  ticketValue,
  handleAssignRackSubmit,
  rackValue,
  isDuplicateTicket,
  modalContainer,
}) => {
  return (
    <Modal
      open={isOpen}
      onClose={() => onClose()}
      showCloseIcon={showCloseIcon}
      classNames={{
        modal: modalContainer,
      }}
      center
    >
      <PopupModalHeader title={title} />
      <Text
        as="p"
        my="30px"
        className="text-center px-20"
        variant="Secondary16Medium125"
      >
        <Text variant="Secondary16Medium125">
          {translation.TICKET}{' '}
          <Text color="primary">{formatTicketNumber(Number(ticketValue))}</Text>{' '}
          {translation.IS_ALREADY_IN_RACK}{' '}
          <Text color={`${!isDuplicateTicket && 'primary'}`}>
            {assignRack}.
          </Text>
          {!isDuplicateTicket &&
            `${' '}${translation.WOULD_YOU_LIKE_TO_MOVE_IT}`}
        </Text>
      </Text>
      <Box className="px-20">
        <Flex sx={{ justifyContent: 'space-between' }}>
          {!isDuplicateTicket && (
            <ThemeButton
              className="font-poppins fw-medium"
              variant="secondary"
              onClick={handleAssignRackSubmit}
            >
              {`${translation.MOVE_TO} ${rackValue}`}
            </ThemeButton>
          )}
          <ThemeButton
            sx={{ fontFamily: 'poppins', fontWeight: '500' }}
            className="custom-cancel-delete-modal-button ms-auto"
            onClick={() => onClose()}
          >
            {isDuplicateTicket ? translation.CLOSE : translation.CANCEL}
          </ThemeButton>
        </Flex>
      </Box>
      <Spinner visible={isVariantLoading} />
    </Modal>
  )
}
