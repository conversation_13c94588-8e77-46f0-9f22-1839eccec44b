'use client'

import React, { useState, useCallback } from 'react'
import { Box, Flex, Text, Input, Select, Card, Label } from 'theme-ui'
import { ThemeButton } from '@/components/core/Button/Button'
import {
  DiscoveredPrinter,
  PrinterConnectionMethod,
  PrinterDiscoveryStatus,
} from '@/types/module/printersModule'
import { showErrorToast } from '@/utils/toastUtils'

interface ManualPrinterEntryProps {
  onClose: () => void
  onPrinterAdd: (printer: DiscoveredPrinter) => void
}

interface ManualPrinterForm {
  name: string
  connectionMethod: PrinterConnectionMethod
  ipAddress: string
  port: string
  macAddress: string
  model: string
  manufacturer: string
}

const ManualPrinterEntry: React.FC<ManualPrinterEntryProps> = ({
  onClose,
  onPrinterAdd,
}) => {
  const [formData, setFormData] = useState<ManualPrinterForm>({
    name: '',
    connectionMethod: PrinterConnectionMethod.NETWORK,
    ipAddress: '',
    port: '9100',
    macAddress: '',
    model: '',
    manufacturer: 'Epson',
  })

  const [isSubmitting, setIsSubmitting] = useState(false)

  const handleInputChange = useCallback((field: keyof ManualPrinterForm, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }))
  }, [])

  const validateForm = (): boolean => {
    if (!formData.name.trim()) {
      showErrorToast('Printer name is required')
      return false
    }

    if (formData.connectionMethod === PrinterConnectionMethod.NETWORK || 
        formData.connectionMethod === PrinterConnectionMethod.ETHERNET) {
      if (!formData.ipAddress.trim()) {
        showErrorToast('IP address is required for network/ethernet connection')
        return false
      }

      // Basic IP validation
      const ipRegex = /^(\d{1,3}\.){3}\d{1,3}$/
      if (!ipRegex.test(formData.ipAddress)) {
        showErrorToast('Please enter a valid IP address')
        return false
      }

      // Port validation
      const port = parseInt(formData.port)
      if (isNaN(port) || port < 1 || port > 65535) {
        showErrorToast('Please enter a valid port number (1-65535)')
        return false
      }
    }

    return true
  }

  const handleSubmit = useCallback(async () => {
    if (!validateForm()) return

    setIsSubmitting(true)

    try {
      const printer: DiscoveredPrinter = {
        id: `manual-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        name: formData.name.trim(),
        model: formData.model.trim() || undefined,
        manufacturer: formData.manufacturer.trim() || 'Unknown',
        connectionMethod: formData.connectionMethod,
        connectionDetails: {
          ipAddress: formData.ipAddress.trim() || undefined,
          port: formData.port ? parseInt(formData.port) : undefined,
          macAddress: formData.macAddress.trim() || undefined,
        },
        capabilities: {
          supportsEpson: formData.manufacturer.toLowerCase().includes('epson'),
          supportedLanguages: ['ESC/POS'],
        },
        status: PrinterDiscoveryStatus.FOUND,
        lastSeen: new Date(),
        isOnline: true,
      }

      onPrinterAdd(printer)
    } catch (error) {
      console.error('Error adding manual printer:', error)
      showErrorToast('Failed to add printer')
    } finally {
      setIsSubmitting(false)
    }
  }, [formData, onPrinterAdd])

  const connectionMethodOptions = [
    { value: PrinterConnectionMethod.NETWORK, label: 'Network (IP)' },
    { value: PrinterConnectionMethod.ETHERNET, label: 'Ethernet (IP)' },
    { value: PrinterConnectionMethod.USB, label: 'USB' },
    { value: PrinterConnectionMethod.BLUETOOTH, label: 'Bluetooth' },
  ]

  const showNetworkFields = formData.connectionMethod === PrinterConnectionMethod.NETWORK || 
                           formData.connectionMethod === PrinterConnectionMethod.ETHERNET

  return (
    <Box
      sx={{
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        bg: 'rgba(0, 0, 0, 0.5)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        zIndex: 1000,
      }}
      onClick={onClose}
    >
      <Card
        sx={{
          maxWidth: '500px',
          width: '90%',
          maxHeight: '90vh',
          overflow: 'auto',
          p: 4,
          bg: 'background',
        }}
        onClick={(e) => e.stopPropagation()}
      >
        {/* Header */}
        <Flex sx={{ justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
          <Text variant="heading" sx={{ fontSize: 3, fontWeight: 'bold' }}>
            Add Printer Manually
          </Text>
          <ThemeButton
            text="✕"
            variant="secondary"
            size="small"
            onClick={onClose}
          />
        </Flex>

        {/* Form */}
        <Box as="form" onSubmit={(e) => { e.preventDefault(); handleSubmit(); }}>
          {/* Printer Name */}
          <Box sx={{ mb: 3 }}>
            <Label htmlFor="printer-name" sx={{ mb: 1, display: 'block', fontWeight: 'bold' }}>
              Printer Name *
            </Label>
            <Input
              id="printer-name"
              placeholder="e.g., Office Printer, Receipt Printer"
              value={formData.name}
              onChange={(e) => handleInputChange('name', e.target.value)}
              sx={{ width: '100%' }}
              required
            />
          </Box>

          {/* Connection Method */}
          <Box sx={{ mb: 3 }}>
            <Label htmlFor="connection-method" sx={{ mb: 1, display: 'block', fontWeight: 'bold' }}>
              Connection Method *
            </Label>
            <Select
              id="connection-method"
              value={formData.connectionMethod}
              onChange={(e) => handleInputChange('connectionMethod', e.target.value as PrinterConnectionMethod)}
              sx={{ width: '100%' }}
            >
              {connectionMethodOptions.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </Select>
          </Box>

          {/* Network/Ethernet Fields */}
          {showNetworkFields && (
            <>
              <Box sx={{ mb: 3 }}>
                <Label htmlFor="ip-address" sx={{ mb: 1, display: 'block', fontWeight: 'bold' }}>
                  IP Address *
                </Label>
                <Input
                  id="ip-address"
                  placeholder="e.g., *************"
                  value={formData.ipAddress}
                  onChange={(e) => handleInputChange('ipAddress', e.target.value)}
                  sx={{ width: '100%' }}
                  required
                />
              </Box>

              <Box sx={{ mb: 3 }}>
                <Label htmlFor="port" sx={{ mb: 1, display: 'block', fontWeight: 'bold' }}>
                  Port
                </Label>
                <Input
                  id="port"
                  type="number"
                  placeholder="9100"
                  value={formData.port}
                  onChange={(e) => handleInputChange('port', e.target.value)}
                  sx={{ width: '100%' }}
                  min="1"
                  max="65535"
                />
                <Text sx={{ fontSize: 0, color: 'muted', mt: 1 }}>
                  Common ports: 9100 (Raw), 515 (LPR), 631 (IPP)
                </Text>
              </Box>
            </>
          )}

          {/* MAC Address */}
          <Box sx={{ mb: 3 }}>
            <Label htmlFor="mac-address" sx={{ mb: 1, display: 'block', fontWeight: 'bold' }}>
              MAC Address (Optional)
            </Label>
            <Input
              id="mac-address"
              placeholder="e.g., 00:11:22:33:44:55"
              value={formData.macAddress}
              onChange={(e) => handleInputChange('macAddress', e.target.value)}
              sx={{ width: '100%' }}
            />
          </Box>

          {/* Manufacturer */}
          <Box sx={{ mb: 3 }}>
            <Label htmlFor="manufacturer" sx={{ mb: 1, display: 'block', fontWeight: 'bold' }}>
              Manufacturer
            </Label>
            <Select
              id="manufacturer"
              value={formData.manufacturer}
              onChange={(e) => handleInputChange('manufacturer', e.target.value)}
              sx={{ width: '100%' }}
            >
              <option value="Epson">Epson</option>
              <option value="Star">Star</option>
              <option value="Zebra">Zebra</option>
              <option value="Brother">Brother</option>
              <option value="Canon">Canon</option>
              <option value="HP">HP</option>
              <option value="Other">Other</option>
            </Select>
          </Box>

          {/* Model */}
          <Box sx={{ mb: 4 }}>
            <Label htmlFor="model" sx={{ mb: 1, display: 'block', fontWeight: 'bold' }}>
              Model (Optional)
            </Label>
            <Input
              id="model"
              placeholder="e.g., TM-T88VI, TM-m30"
              value={formData.model}
              onChange={(e) => handleInputChange('model', e.target.value)}
              sx={{ width: '100%' }}
            />
          </Box>

          {/* Actions */}
          <Flex sx={{ justifyContent: 'flex-end', gap: 2 }}>
            <ThemeButton
              text="Cancel"
              variant="secondary"
              onClick={onClose}
              disabled={isSubmitting}
            />
            <ThemeButton
              text={isSubmitting ? 'Adding...' : 'Add Printer'}
              variant="primary"
              type="submit"
              disabled={isSubmitting}
            />
          </Flex>
        </Box>

        {/* Help Text */}
        <Box sx={{ mt: 4, p: 3, bg: 'muted', borderRadius: 1 }}>
          <Text sx={{ fontSize: 1, fontWeight: 'bold', mb: 2 }}>
            Tips for Manual Entry:
          </Text>
          <Box sx={{ fontSize: 0, lineHeight: 1.4 }}>
            <Text sx={{ display: 'block', mb: 1 }}>
              • For network printers, ensure the printer is on the same network
            </Text>
            <Text sx={{ display: 'block', mb: 1 }}>
              • Check printer settings for IP address and port information
            </Text>
            <Text sx={{ display: 'block', mb: 1 }}>
              • MAC address can usually be found in printer network settings
            </Text>
            <Text sx={{ display: 'block', mb: 1 }}>
              • Test the connection after adding to verify functionality
            </Text>
          </Box>
        </Box>
      </Card>
    </Box>
  )
}

export default ManualPrinterEntry
