'use client'

import React, { useState, useCallback, useEffect } from 'react'
import { Box, Flex, Text, Button, Card, Spinner } from 'theme-ui'
import { ThemeButton } from '@/components/core/Button/Button'
import { translation } from '@/utils/translation'
import {
  PrinterConnectionMethod,
  PrinterDiscoveryOptions,
  PrinterDiscoveryResult,
  DiscoveredPrinter,
  PrinterDiscoveryStatus,
  EpsonTestPrintData,
} from '@/types/module/printersModule'
import PrinterDiscoveryService from '@/services/printerDiscovery.service'
import ConnectionMethodSelector from './ConnectionMethodSelector'
import DiscoveredPrintersList from './DiscoveredPrintersList'
import ManualPrinterEntry from './ManualPrinterEntry'
import TestPrintDialog from './TestPrintDialog'
import PrinterManagement from './PrinterManagement'
import {
  showSuccessToast,
  showErrorToast,
  showDefaultToast,
} from '@/utils/toastUtils'

interface PrinterDiscoveryMainProps {
  onPrinterSelected?: (printer: DiscoveredPrinter) => void
  onClose?: () => void
}

const PrinterDiscoveryMain: React.FC<PrinterDiscoveryMainProps> = ({
  onPrinterSelected,
  onClose,
}) => {
  const [selectedMethods, setSelectedMethods] = useState<
    PrinterConnectionMethod[]
  >([PrinterConnectionMethod.NETWORK])
  const [isScanning, setIsScanning] = useState(false)
  const [discoveredPrinters, setDiscoveredPrinters] = useState<
    DiscoveredPrinter[]
  >([])
  const [scanResults, setScanResults] = useState<PrinterDiscoveryResult | null>(
    null
  )
  const [showManualEntry, setShowManualEntry] = useState(false)
  const [selectedPrinter, setSelectedPrinter] =
    useState<DiscoveredPrinter | null>(null)
  const [showTestDialog, setShowTestDialog] = useState(false)
  const [scanProgress, setScanProgress] = useState<string>('')
  const [activeTab, setActiveTab] = useState<'discovery' | 'management'>(
    'discovery'
  )

  const printerDiscoveryService = PrinterDiscoveryService

  // Handle connection method selection
  const handleMethodToggle = useCallback((method: PrinterConnectionMethod) => {
    setSelectedMethods((prev) => {
      if (prev.includes(method)) {
        return prev.filter((m) => m !== method)
      } else {
        return [...prev, method]
      }
    })
  }, [])

  // Start printer discovery
  const handleStartDiscovery = useCallback(async () => {
    if (selectedMethods.length === 0) {
      showErrorToast('Please select at least one connection method')
      return
    }

    setIsScanning(true)
    setScanProgress('Initializing scan...')
    setDiscoveredPrinters([])
    setScanResults(null)

    try {
      const options: PrinterDiscoveryOptions = {
        connectionMethods: selectedMethods,
        timeout: 5000,
        epsonOnly: true,
        includeOffline: false,
      }

      setScanProgress('Scanning for printers...')
      const result = await printerDiscoveryService.discoverPrinters(options)

      setScanResults(result)
      setDiscoveredPrinters(result.printers)

      if (result.success) {
        if (result.printers.length > 0) {
          showSuccessToast(`Found ${result.printers.length} printer(s)`)
        } else {
          showDefaultToast(
            'No printers found. Try different connection methods or add manually.'
          )
        }
      } else {
        showErrorToast(
          'Discovery failed. Check connection methods and try again.'
        )
      }

      if (result.errors && result.errors.length > 0) {
        console.warn('Discovery errors:', result.errors)
      }
    } catch (error) {
      console.error('Discovery error:', error)
      showErrorToast('Failed to discover printers. Please try again.')
    } finally {
      setIsScanning(false)
      setScanProgress('')
    }
  }, [selectedMethods, printerDiscoveryService])

  // Stop discovery
  const handleStopDiscovery = useCallback(() => {
    printerDiscoveryService.stopDiscovery()
    setIsScanning(false)
    setScanProgress('')
  }, [printerDiscoveryService])

  // Handle printer selection
  const handlePrinterSelect = useCallback(
    (printer: DiscoveredPrinter) => {
      setSelectedPrinter(printer)
      if (onPrinterSelected) {
        onPrinterSelected(printer)
      }
    },
    [onPrinterSelected]
  )

  // Handle test print
  const handleTestPrint = useCallback((printer: DiscoveredPrinter) => {
    setSelectedPrinter(printer)
    setShowTestDialog(true)
  }, [])

  // Execute test print
  const handleExecuteTestPrint = useCallback(
    async (testData: EpsonTestPrintData) => {
      if (!selectedPrinter) return

      try {
        showDefaultToast('Sending test print...')
        const result = await printerDiscoveryService.testPrint(
          selectedPrinter,
          testData
        )

        if (result.success) {
          showSuccessToast('Test print sent successfully!')
          // Update printer in list
          setDiscoveredPrinters((prev) =>
            prev.map((p) =>
              p.id === selectedPrinter.id
                ? { ...p, status: PrinterDiscoveryStatus.TEST_SUCCESS }
                : p
            )
          )
        } else {
          showErrorToast(
            `Test print failed: ${result.error || 'Unknown error'}`
          )
          setDiscoveredPrinters((prev) =>
            prev.map((p) =>
              p.id === selectedPrinter.id
                ? { ...p, status: PrinterDiscoveryStatus.TEST_FAILED }
                : p
            )
          )
        }
      } catch (error) {
        console.error('Test print error:', error)
        showErrorToast('Failed to send test print')
      } finally {
        setShowTestDialog(false)
        setSelectedPrinter(null)
      }
    },
    [selectedPrinter, printerDiscoveryService]
  )

  // Handle manual printer addition
  const handleManualPrinterAdd = useCallback((printer: DiscoveredPrinter) => {
    setDiscoveredPrinters((prev) => [...prev, printer])
    setShowManualEntry(false)
    showSuccessToast('Printer added manually')
  }, [])

  // Clear results
  const handleClearResults = useCallback(() => {
    setDiscoveredPrinters([])
    setScanResults(null)
    printerDiscoveryService.clearCache()
  }, [printerDiscoveryService])

  return (
    <Box sx={{ p: 4, maxWidth: '1200px', mx: 'auto' }}>
      {/* Header */}
      <Flex
        sx={{ justifyContent: 'space-between', alignItems: 'center', mb: 4 }}
      >
        <Text variant="heading" sx={{ fontSize: 4, fontWeight: 'bold' }}>
          Printer Discovery & Management
        </Text>
        {onClose && (
          <ThemeButton text="Close" variant="secondary" onClick={onClose} />
        )}
      </Flex>

      {/* Tab Navigation */}
      <Flex sx={{ mb: 4, borderBottom: '2px solid', borderColor: 'muted' }}>
        <Button
          sx={{
            px: 4,
            py: 2,
            bg: activeTab === 'discovery' ? 'primary' : 'transparent',
            color: activeTab === 'discovery' ? 'white' : 'text',
            border: 'none',
            borderRadius: '4px 4px 0 0',
            cursor: 'pointer',
            fontWeight: 'bold',
            '&:hover': {
              bg: activeTab === 'discovery' ? 'primary' : 'muted',
            },
          }}
          onClick={() => setActiveTab('discovery')}
        >
          🔍 Discovery
        </Button>
        <Button
          sx={{
            px: 4,
            py: 2,
            bg: activeTab === 'management' ? 'primary' : 'transparent',
            color: activeTab === 'management' ? 'white' : 'text',
            border: 'none',
            borderRadius: '4px 4px 0 0',
            cursor: 'pointer',
            fontWeight: 'bold',
            ml: 1,
            '&:hover': {
              bg: activeTab === 'management' ? 'primary' : 'muted',
            },
          }}
          onClick={() => setActiveTab('management')}
        >
          🔧 Management ({discoveredPrinters.length})
        </Button>
      </Flex>

      {/* Discovery Tab Content */}
      {activeTab === 'discovery' && (
        <>
          {/* Connection Method Selection */}
          <Card sx={{ p: 3, mb: 4 }}>
            <Text variant="subheading" sx={{ mb: 3, fontWeight: 'bold' }}>
              Select Connection Methods
            </Text>
            <ConnectionMethodSelector
              selectedMethods={selectedMethods}
              onMethodToggle={handleMethodToggle}
              disabled={isScanning}
            />
          </Card>

          {/* Discovery Controls */}
          <Card sx={{ p: 3, mb: 4 }}>
            <Flex
              sx={{
                justifyContent: 'space-between',
                alignItems: 'center',
                mb: 3,
              }}
            >
              <Text variant="subheading" sx={{ fontWeight: 'bold' }}>
                Printer Discovery
              </Text>
              <Flex sx={{ gap: 2 }}>
                <ThemeButton
                  text="Add Manually"
                  variant="secondary"
                  onClick={() => setShowManualEntry(true)}
                  disabled={isScanning}
                />
                {discoveredPrinters.length > 0 && (
                  <ThemeButton
                    text="Clear Results"
                    variant="secondary"
                    onClick={handleClearResults}
                    disabled={isScanning}
                  />
                )}
                {isScanning ? (
                  <ThemeButton
                    text="Stop Scan"
                    variant="danger"
                    onClick={handleStopDiscovery}
                  />
                ) : (
                  <ThemeButton
                    text="Start Discovery"
                    variant="primary"
                    onClick={handleStartDiscovery}
                    disabled={selectedMethods.length === 0}
                  />
                )}
              </Flex>
            </Flex>

            {/* Scanning Progress */}
            {isScanning && (
              <Flex sx={{ alignItems: 'center', gap: 2, mb: 3 }}>
                <Spinner size={20} />
                <Text>{scanProgress}</Text>
              </Flex>
            )}

            {/* Scan Results Summary */}
            {scanResults && !isScanning && (
              <Box sx={{ mb: 3, p: 2, bg: 'muted', borderRadius: 1 }}>
                <Text sx={{ fontSize: 1 }}>
                  Scan completed in {scanResults.scanDuration}ms - Found{' '}
                  {scanResults.printers.length} printer(s)
                  {scanResults.errors && scanResults.errors.length > 0 && (
                    <span style={{ color: 'orange' }}>
                      {' '}
                      ({scanResults.errors.length} error(s))
                    </span>
                  )}
                </Text>
              </Box>
            )}
          </Card>

          {/* Discovered Printers List */}
          {discoveredPrinters.length > 0 && (
            <Card sx={{ p: 3, mb: 4 }}>
              <Text variant="subheading" sx={{ mb: 3, fontWeight: 'bold' }}>
                Discovered Printers ({discoveredPrinters.length})
              </Text>
              <DiscoveredPrintersList
                printers={discoveredPrinters}
                onPrinterSelect={handlePrinterSelect}
                onTestPrint={handleTestPrint}
                selectedPrinter={selectedPrinter}
              />
            </Card>
          )}

          {/* Help Text */}
          <Box sx={{ mt: 4, p: 3, bg: 'muted', borderRadius: 1 }}>
            <Text sx={{ fontSize: 1, color: 'text' }}>
              <strong>Tips:</strong>
              <br />
              • Network: Scans your local network for Epson printers
              <br />
              • USB: Requires browser permission to access USB devices
              <br />
              • Bluetooth: Requires browser permission and printer pairing
              <br />• Manual: Add printer details manually if auto-discovery
              fails
            </Text>
          </Box>
        </>
      )}

      {/* Management Tab Content */}
      {activeTab === 'management' && (
        <PrinterManagement
          discoveredPrinters={discoveredPrinters}
          onPrinterUpdated={(updatedPrinter) => {
            setDiscoveredPrinters((prev) =>
              prev.map((p) => (p.id === updatedPrinter.id ? updatedPrinter : p))
            )
          }}
        />
      )}

      {/* Manual Printer Entry Modal */}
      {showManualEntry && (
        <ManualPrinterEntry
          onClose={() => setShowManualEntry(false)}
          onPrinterAdd={handleManualPrinterAdd}
        />
      )}

      {/* Test Print Dialog */}
      {showTestDialog && selectedPrinter && (
        <TestPrintDialog
          printer={selectedPrinter}
          onClose={() => {
            setShowTestDialog(false)
            setSelectedPrinter(null)
          }}
          onTestPrint={handleExecuteTestPrint}
        />
      )}
    </Box>
  )
}

export default PrinterDiscoveryMain
