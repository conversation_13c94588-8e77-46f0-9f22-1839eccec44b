'use client'

import React from 'react'
import { Box, Flex, Text, Card, Badge } from 'theme-ui'
import { ThemeButton } from '@/components/core/Button/Button'
import {
  DiscoveredPrinter,
  PrinterConnectionMethod,
  PrinterDiscoveryStatus,
} from '@/types/module/printersModule'

interface DiscoveredPrintersListProps {
  printers: DiscoveredPrinter[]
  onPrinterSelect: (printer: DiscoveredPrinter) => void
  onTestPrint: (printer: DiscoveredPrinter) => void
  selectedPrinter?: DiscoveredPrinter | null
}

const DiscoveredPrintersList: React.FC<DiscoveredPrintersListProps> = ({
  printers,
  onPrinterSelect,
  onTestPrint,
  selectedPrinter,
}) => {
  const getConnectionMethodIcon = (method: PrinterConnectionMethod): string => {
    switch (method) {
      case PrinterConnectionMethod.NETWORK:
        return '🌐'
      case PrinterConnectionMethod.USB:
        return '🔌'
      case PrinterConnectionMethod.BLUETOOTH:
        return '📶'
      case PrinterConnectionMethod.ETHERNET:
        return '🔗'
      case PrinterConnectionMethod.MANUAL:
        return '✏️'
      default:
        return '🖨️'
    }
  }

  const getStatusColor = (status: PrinterDiscoveryStatus): string => {
    switch (status) {
      case PrinterDiscoveryStatus.FOUND:
        return 'blue'
      case PrinterDiscoveryStatus.CONNECTED:
        return 'green'
      case PrinterDiscoveryStatus.CONNECTING:
        return 'orange'
      case PrinterDiscoveryStatus.TESTING:
        return 'purple'
      case PrinterDiscoveryStatus.TEST_SUCCESS:
        return 'green'
      case PrinterDiscoveryStatus.TEST_FAILED:
        return 'red'
      case PrinterDiscoveryStatus.ERROR:
        return 'red'
      default:
        return 'gray'
    }
  }

  const getStatusText = (status: PrinterDiscoveryStatus): string => {
    switch (status) {
      case PrinterDiscoveryStatus.FOUND:
        return 'Found'
      case PrinterDiscoveryStatus.CONNECTED:
        return 'Connected'
      case PrinterDiscoveryStatus.CONNECTING:
        return 'Connecting...'
      case PrinterDiscoveryStatus.TESTING:
        return 'Testing...'
      case PrinterDiscoveryStatus.TEST_SUCCESS:
        return 'Test Passed'
      case PrinterDiscoveryStatus.TEST_FAILED:
        return 'Test Failed'
      case PrinterDiscoveryStatus.ERROR:
        return 'Error'
      default:
        return 'Unknown'
    }
  }

  const isSelected = (printer: DiscoveredPrinter): boolean => {
    return selectedPrinter?.id === printer.id
  }

  const canTestPrint = (printer: DiscoveredPrinter): boolean => {
    return ![
      PrinterDiscoveryStatus.TESTING,
      PrinterDiscoveryStatus.CONNECTING,
    ].includes(printer.status)
  }

  const formatConnectionDetails = (printer: DiscoveredPrinter): string => {
    const details = printer.connectionDetails
    switch (printer.connectionMethod) {
      case PrinterConnectionMethod.NETWORK:
      case PrinterConnectionMethod.ETHERNET:
        return details.ipAddress 
          ? `${details.ipAddress}${details.port ? `:${details.port}` : ''}`
          : 'Network Printer'
      case PrinterConnectionMethod.USB:
        return `USB (${details.usbVendorId}:${details.usbProductId})`
      case PrinterConnectionMethod.BLUETOOTH:
        return `Bluetooth (${details.bluetoothId || 'Unknown ID'})`
      case PrinterConnectionMethod.MANUAL:
        return 'Manually Added'
      default:
        return 'Unknown Connection'
    }
  }

  if (printers.length === 0) {
    return (
      <Box sx={{ textAlign: 'center', py: 4 }}>
        <Text sx={{ fontSize: 3, mb: 2 }}>🔍</Text>
        <Text sx={{ color: 'muted' }}>No printers discovered yet</Text>
      </Box>
    )
  }

  return (
    <Box>
      {printers.map((printer) => (
        <Card
          key={printer.id}
          sx={{
            mb: 3,
            p: 3,
            border: '2px solid',
            borderColor: isSelected(printer) ? 'primary' : 'muted',
            bg: isSelected(printer) ? 'primary' : 'background',
            color: isSelected(printer) ? 'white' : 'text',
            cursor: 'pointer',
            transition: 'all 0.2s ease',
            '&:hover': {
              borderColor: 'primary',
              transform: 'translateY(-1px)',
              boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
            },
          }}
          onClick={() => onPrinterSelect(printer)}
        >
          <Flex sx={{ alignItems: 'flex-start', justifyContent: 'space-between' }}>
            {/* Printer Info */}
            <Box sx={{ flex: 1 }}>
              {/* Header */}
              <Flex sx={{ alignItems: 'center', mb: 2 }}>
                <Text sx={{ fontSize: 3, mr: 2 }}>
                  {getConnectionMethodIcon(printer.connectionMethod)}
                </Text>
                <Box sx={{ flex: 1 }}>
                  <Text 
                    sx={{ 
                      fontWeight: 'bold', 
                      fontSize: 2,
                      color: isSelected(printer) ? 'white' : 'text'
                    }}
                  >
                    {printer.name}
                  </Text>
                  {printer.model && (
                    <Text 
                      sx={{ 
                        fontSize: 1, 
                        color: isSelected(printer) ? 'white' : 'muted'
                      }}
                    >
                      {printer.manufacturer} {printer.model}
                    </Text>
                  )}
                </Box>
                <Badge
                  sx={{
                    bg: getStatusColor(printer.status),
                    color: 'white',
                    fontSize: 0,
                  }}
                >
                  {getStatusText(printer.status)}
                </Badge>
              </Flex>

              {/* Connection Details */}
              <Box sx={{ mb: 2 }}>
                <Text 
                  sx={{ 
                    fontSize: 1,
                    color: isSelected(printer) ? 'white' : 'text'
                  }}
                >
                  <strong>Connection:</strong> {printer.connectionMethod}
                </Text>
                <Text 
                  sx={{ 
                    fontSize: 1,
                    color: isSelected(printer) ? 'white' : 'muted'
                  }}
                >
                  {formatConnectionDetails(printer)}
                </Text>
              </Box>

              {/* Additional Info */}
              <Flex sx={{ flexWrap: 'wrap', gap: 2, mb: 2 }}>
                {printer.signal && (
                  <Box>
                    <Text 
                      sx={{ 
                        fontSize: 0,
                        color: isSelected(printer) ? 'white' : 'muted'
                      }}
                    >
                      Signal: {printer.signal}%
                    </Text>
                  </Box>
                )}
                {printer.isOnline !== undefined && (
                  <Box>
                    <Text 
                      sx={{ 
                        fontSize: 0,
                        color: printer.isOnline 
                          ? isSelected(printer) ? 'white' : 'green'
                          : 'red'
                      }}
                    >
                      {printer.isOnline ? '● Online' : '● Offline'}
                    </Text>
                  </Box>
                )}
                {printer.lastSeen && (
                  <Box>
                    <Text 
                      sx={{ 
                        fontSize: 0,
                        color: isSelected(printer) ? 'white' : 'muted'
                      }}
                    >
                      Last seen: {printer.lastSeen.toLocaleTimeString()}
                    </Text>
                  </Box>
                )}
              </Flex>

              {/* Capabilities */}
              {printer.capabilities && (
                <Box sx={{ mb: 2 }}>
                  <Text 
                    sx={{ 
                      fontSize: 0, 
                      fontWeight: 'bold',
                      color: isSelected(printer) ? 'white' : 'text'
                    }}
                  >
                    Capabilities:
                  </Text>
                  <Flex sx={{ flexWrap: 'wrap', gap: 1, mt: 1 }}>
                    {printer.capabilities.supportsEpson && (
                      <Badge 
                        sx={{ 
                          bg: 'green', 
                          color: 'white', 
                          fontSize: 0 
                        }}
                      >
                        Epson Compatible
                      </Badge>
                    )}
                    {printer.capabilities.supportedLanguages?.map((lang) => (
                      <Badge 
                        key={lang}
                        sx={{ 
                          bg: 'blue', 
                          color: 'white', 
                          fontSize: 0 
                        }}
                      >
                        {lang}
                      </Badge>
                    ))}
                  </Flex>
                </Box>
              )}
            </Box>

            {/* Actions */}
            <Box sx={{ ml: 3, display: 'flex', flexDirection: 'column', gap: 2 }}>
              <ThemeButton
                text="Test Print"
                variant={isSelected(printer) ? 'secondary' : 'primary'}
                size="small"
                onClick={(e) => {
                  e.stopPropagation()
                  onTestPrint(printer)
                }}
                disabled={!canTestPrint(printer)}
              />
              
              {isSelected(printer) && (
                <Badge
                  sx={{
                    bg: 'white',
                    color: 'primary',
                    fontSize: 0,
                    textAlign: 'center',
                    py: 1,
                  }}
                >
                  Selected
                </Badge>
              )}
            </Box>
          </Flex>

          {/* MAC Address (if available) */}
          {printer.connectionDetails.macAddress && (
            <Box sx={{ mt: 2, pt: 2, borderTop: '1px solid', borderColor: 'muted' }}>
              <Text 
                sx={{ 
                  fontSize: 0,
                  color: isSelected(printer) ? 'white' : 'muted',
                  fontFamily: 'monospace'
                }}
              >
                MAC: {printer.connectionDetails.macAddress}
              </Text>
            </Box>
          )}
        </Card>
      ))}

      {/* Summary */}
      <Box sx={{ mt: 3, p: 2, bg: 'muted', borderRadius: 1 }}>
        <Text sx={{ fontSize: 1 }}>
          Total: {printers.length} printer(s) • 
          Online: {printers.filter(p => p.isOnline).length} • 
          Tested: {printers.filter(p => p.status === PrinterDiscoveryStatus.TEST_SUCCESS).length}
        </Text>
      </Box>
    </Box>
  )
}

export default DiscoveredPrintersList
