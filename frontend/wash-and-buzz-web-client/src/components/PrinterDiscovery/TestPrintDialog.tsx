'use client'

import React, { useState, useCallback } from 'react'
import { Box, Flex, Text, Card, Label, Checkbox, Textarea } from 'theme-ui'
import { ThemeButton } from '@/components/core/Button/Button'
import {
  DiscoveredPrinter,
  EpsonTestPrintData,
} from '@/types/module/printersModule'

interface TestPrintDialogProps {
  printer: DiscoveredPrinter
  onClose: () => void
  onTestPrint: (testData: EpsonTestPrintData) => void
}

const TestPrintDialog: React.FC<TestPrintDialogProps> = ({
  printer,
  onClose,
  onTestPrint,
}) => {
  const [testData, setTestData] = useState<EpsonTestPrintData>({
    storeName: 'Wash & Buzz Test Store',
    storeAddress: '123 Test Street, Test City',
    testPattern: true,
    includeBarcode: true,
    includeQRCode: true,
    customText: '',
  })

  const [isSubmitting, setIsSubmitting] = useState(false)
  const [selectedTestType, setSelectedTestType] = useState<'full' | 'simple' | 'minimal'>('simple')

  const handleInputChange = useCallback((field: keyof EpsonTestPrintData, value: any) => {
    setTestData(prev => ({
      ...prev,
      [field]: value,
    }))
  }, [])

  const handleTestTypeChange = useCallback((type: 'full' | 'simple' | 'minimal') => {
    setSelectedTestType(type)
    
    // Update test data based on selected type
    switch (type) {
      case 'minimal':
        setTestData(prev => ({
          ...prev,
          testPattern: false,
          includeBarcode: false,
          includeQRCode: false,
          customText: '',
        }))
        break
      case 'simple':
        setTestData(prev => ({
          ...prev,
          testPattern: false,
          includeBarcode: true,
          includeQRCode: false,
          customText: '',
        }))
        break
      case 'full':
        setTestData(prev => ({
          ...prev,
          testPattern: true,
          includeBarcode: true,
          includeQRCode: true,
        }))
        break
    }
  }, [])

  const handleSubmit = useCallback(async () => {
    setIsSubmitting(true)
    try {
      await onTestPrint(testData)
    } finally {
      setIsSubmitting(false)
    }
  }, [testData, onTestPrint])

  const testTypeOptions = [
    {
      type: 'minimal' as const,
      label: 'Minimal Test',
      description: 'Basic text only - fastest test',
      icon: '📄',
    },
    {
      type: 'simple' as const,
      label: 'Simple Test',
      description: 'Text + barcode - recommended',
      icon: '📋',
    },
    {
      type: 'full' as const,
      label: 'Full Test',
      description: 'All features - comprehensive test',
      icon: '📊',
    },
  ]

  return (
    <Box
      sx={{
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        bg: 'rgba(0, 0, 0, 0.5)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        zIndex: 1000,
      }}
      onClick={onClose}
    >
      <Card
        sx={{
          maxWidth: '600px',
          width: '90%',
          maxHeight: '90vh',
          overflow: 'auto',
          p: 4,
          bg: 'background',
        }}
        onClick={(e) => e.stopPropagation()}
      >
        {/* Header */}
        <Flex sx={{ justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
          <Box>
            <Text variant="heading" sx={{ fontSize: 3, fontWeight: 'bold' }}>
              Test Print
            </Text>
            <Text sx={{ fontSize: 1, color: 'muted' }}>
              {printer.name} ({printer.connectionMethod})
            </Text>
          </Box>
          <ThemeButton
            text="✕"
            variant="secondary"
            size="small"
            onClick={onClose}
          />
        </Flex>

        {/* Test Type Selection */}
        <Box sx={{ mb: 4 }}>
          <Text sx={{ fontWeight: 'bold', mb: 2 }}>Test Type</Text>
          <Flex sx={{ flexDirection: 'column', gap: 2 }}>
            {testTypeOptions.map((option) => (
              <Card
                key={option.type}
                sx={{
                  p: 3,
                  cursor: 'pointer',
                  border: '2px solid',
                  borderColor: selectedTestType === option.type ? 'primary' : 'muted',
                  bg: selectedTestType === option.type ? 'primary' : 'background',
                  color: selectedTestType === option.type ? 'white' : 'text',
                  transition: 'all 0.2s ease',
                }}
                onClick={() => handleTestTypeChange(option.type)}
              >
                <Flex sx={{ alignItems: 'center' }}>
                  <Text sx={{ fontSize: 3, mr: 3 }}>{option.icon}</Text>
                  <Box sx={{ flex: 1 }}>
                    <Text sx={{ fontWeight: 'bold', mb: 1 }}>
                      {option.label}
                    </Text>
                    <Text sx={{ fontSize: 1 }}>
                      {option.description}
                    </Text>
                  </Box>
                  {selectedTestType === option.type && (
                    <Text sx={{ fontSize: 2 }}>✓</Text>
                  )}
                </Flex>
              </Card>
            ))}
          </Flex>
        </Box>

        {/* Store Information */}
        <Box sx={{ mb: 4 }}>
          <Text sx={{ fontWeight: 'bold', mb: 2 }}>Store Information</Text>
          
          <Box sx={{ mb: 3 }}>
            <Label htmlFor="store-name" sx={{ mb: 1, display: 'block' }}>
              Store Name
            </Label>
            <input
              id="store-name"
              type="text"
              value={testData.storeName || ''}
              onChange={(e) => handleInputChange('storeName', e.target.value)}
              style={{
                width: '100%',
                padding: '8px',
                border: '1px solid #ccc',
                borderRadius: '4px',
              }}
            />
          </Box>

          <Box sx={{ mb: 3 }}>
            <Label htmlFor="store-address" sx={{ mb: 1, display: 'block' }}>
              Store Address
            </Label>
            <input
              id="store-address"
              type="text"
              value={testData.storeAddress || ''}
              onChange={(e) => handleInputChange('storeAddress', e.target.value)}
              style={{
                width: '100%',
                padding: '8px',
                border: '1px solid #ccc',
                borderRadius: '4px',
              }}
            />
          </Box>
        </Box>

        {/* Test Options */}
        <Box sx={{ mb: 4 }}>
          <Text sx={{ fontWeight: 'bold', mb: 2 }}>Test Options</Text>
          
          <Box sx={{ mb: 2 }}>
            <Label sx={{ display: 'flex', alignItems: 'center', cursor: 'pointer' }}>
              <Checkbox
                checked={testData.testPattern}
                onChange={(e) => handleInputChange('testPattern', e.target.checked)}
                disabled={selectedTestType === 'minimal' || selectedTestType === 'simple'}
              />
              <Text sx={{ ml: 2 }}>Include test pattern (grid, density test)</Text>
            </Label>
          </Box>

          <Box sx={{ mb: 2 }}>
            <Label sx={{ display: 'flex', alignItems: 'center', cursor: 'pointer' }}>
              <Checkbox
                checked={testData.includeBarcode}
                onChange={(e) => handleInputChange('includeBarcode', e.target.checked)}
                disabled={selectedTestType === 'minimal'}
              />
              <Text sx={{ ml: 2 }}>Include barcode test</Text>
            </Label>
          </Box>

          <Box sx={{ mb: 2 }}>
            <Label sx={{ display: 'flex', alignItems: 'center', cursor: 'pointer' }}>
              <Checkbox
                checked={testData.includeQRCode}
                onChange={(e) => handleInputChange('includeQRCode', e.target.checked)}
                disabled={selectedTestType === 'minimal' || selectedTestType === 'simple'}
              />
              <Text sx={{ ml: 2 }}>Include QR code test</Text>
            </Label>
          </Box>
        </Box>

        {/* Custom Text */}
        <Box sx={{ mb: 4 }}>
          <Label htmlFor="custom-text" sx={{ mb: 1, display: 'block', fontWeight: 'bold' }}>
            Custom Text (Optional)
          </Label>
          <Textarea
            id="custom-text"
            placeholder="Add any custom text to include in the test print..."
            value={testData.customText || ''}
            onChange={(e) => handleInputChange('customText', e.target.value)}
            rows={3}
            sx={{ width: '100%' }}
          />
        </Box>

        {/* Preview */}
        <Box sx={{ mb: 4, p: 3, bg: 'muted', borderRadius: 1 }}>
          <Text sx={{ fontWeight: 'bold', mb: 2 }}>Test Print Preview</Text>
          <Box sx={{ fontSize: 0, fontFamily: 'monospace', lineHeight: 1.4 }}>
            <Text sx={{ display: 'block' }}>📄 PRINTER TEST</Text>
            {testData.storeName && (
              <Text sx={{ display: 'block' }}>🏪 {testData.storeName}</Text>
            )}
            {testData.storeAddress && (
              <Text sx={{ display: 'block' }}>📍 {testData.storeAddress}</Text>
            )}
            <Text sx={{ display: 'block' }}>📅 {new Date().toLocaleString()}</Text>
            {testData.includeBarcode && (
              <Text sx={{ display: 'block' }}>📊 Barcode Test</Text>
            )}
            {testData.includeQRCode && (
              <Text sx={{ display: 'block' }}>🔲 QR Code Test</Text>
            )}
            {testData.testPattern && (
              <Text sx={{ display: 'block' }}>🎨 Test Patterns</Text>
            )}
            {testData.customText && (
              <Text sx={{ display: 'block' }}>💬 {testData.customText}</Text>
            )}
            <Text sx={{ display: 'block' }}>✅ Test Complete</Text>
          </Box>
        </Box>

        {/* Actions */}
        <Flex sx={{ justifyContent: 'flex-end', gap: 2 }}>
          <ThemeButton
            text="Cancel"
            variant="secondary"
            onClick={onClose}
            disabled={isSubmitting}
          />
          <ThemeButton
            text={isSubmitting ? 'Printing...' : 'Send Test Print'}
            variant="primary"
            onClick={handleSubmit}
            disabled={isSubmitting}
          />
        </Flex>

        {/* Warning */}
        <Box sx={{ mt: 3, p: 2, bg: 'orange', color: 'white', borderRadius: 1 }}>
          <Text sx={{ fontSize: 0, fontWeight: 'bold' }}>
            ⚠️ Note: This will send a test print to the selected printer. 
            Make sure the printer has paper loaded and is ready to print.
          </Text>
        </Box>
      </Card>
    </Box>
  )
}

export default TestPrintDialog
