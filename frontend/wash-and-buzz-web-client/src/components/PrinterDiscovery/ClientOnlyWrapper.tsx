'use client'

import React, { useState, useEffect } from 'react'
import { Box, Text, Spinner } from 'theme-ui'

interface ClientOnlyWrapperProps {
  children: React.ReactNode
  fallback?: React.ReactNode
}

/**
 * Client-only wrapper to prevent hydration mismatches
 * This component only renders its children on the client side
 */
const ClientOnlyWrapper: React.FC<ClientOnlyWrapperProps> = ({
  children,
  fallback = (
    <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', p: 4 }}>
      <Spinner size={24} sx={{ mr: 2 }} />
      <Text>Loading printer discovery...</Text>
    </Box>
  ),
}) => {
  const [hasMounted, setHasMounted] = useState(false)

  useEffect(() => {
    setHasMounted(true)
  }, [])

  if (!hasMounted) {
    return <>{fallback}</>
  }

  return <>{children}</>
}

export default ClientOnlyWrapper
