'use client'

import React, { useState } from 'react'
import { Box, Text, Card, Flex } from 'theme-ui'
import { ThemeButton } from '@/components/core/Button/Button'
import { showSuccessToast, showErrorToast, showDefaultToast } from '@/utils/toastUtils'

const USBDebugPanel: React.FC = () => {
  const [debugInfo, setDebugInfo] = useState<string[]>([])
  const [isLoading, setIsLoading] = useState(false)

  const addDebugLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString()
    setDebugInfo(prev => [...prev, `[${timestamp}] ${message}`])
  }

  const clearLogs = () => {
    setDebugInfo([])
  }

  const checkWebUSBSupport = () => {
    addDebugLog('🔍 Checking WebUSB support...')
    
    if (typeof navigator === 'undefined') {
      addDebugLog('❌ Navigator not available (SSR)')
      return false
    }
    
    if (!('usb' in navigator)) {
      addDebugLog('❌ WebUSB not supported in this browser')
      addDebugLog('💡 Try using Chrome or Edge')
      return false
    }
    
    addDebugLog('✅ WebUSB is supported!')
    return true
  }

  const getPairedDevices = async () => {
    if (!checkWebUSBSupport()) return
    
    try {
      addDebugLog('📋 Getting already paired USB devices...')
      const devices = await navigator.usb.getDevices()
      addDebugLog(`Found ${devices.length} paired devices`)
      
      if (devices.length === 0) {
        addDebugLog('ℹ️ No devices are currently paired')
        addDebugLog('💡 You need to pair devices first using requestDevice()')
      } else {
        devices.forEach((device, index) => {
          addDebugLog(`${index + 1}. ${device.manufacturerName || 'Unknown'} ${device.productName || 'Unknown Device'}`)
          addDebugLog(`   Vendor: 0x${device.vendorId.toString(16).padStart(4, '0')}, Product: 0x${device.productId.toString(16).padStart(4, '0')}`)
        })
      }
    } catch (error) {
      addDebugLog(`❌ Error getting paired devices: ${error}`)
    }
  }

  const requestUSBDevice = async () => {
    if (!checkWebUSBSupport()) return
    
    setIsLoading(true)
    try {
      addDebugLog('🚀 Requesting USB device access...')
      addDebugLog('📱 Browser dialog should open - select your USB device')
      
      // Use comprehensive filters to show all devices
      const device = await navigator.usb.requestDevice({
        filters: [
          // Input devices (mouse, keyboard)
          { classCode: 3 },
          // Common vendors
          { vendorId: 0x046d }, // Logitech
          { vendorId: 0x045e }, // Microsoft
          { vendorId: 0x1532 }, // Razer
          { vendorId: 0x04f2 }, // Chicony
          { vendorId: 0x093a }, // Pixart
          // Storage devices
          { classCode: 8 },
          // Any device fallback
          {}
        ]
      })

      if (device) {
        addDebugLog('🎉 Device selected successfully!')
        addDebugLog(`📋 Name: ${device.manufacturerName || 'Unknown'} ${device.productName || 'Unknown Device'}`)
        addDebugLog(`📋 Vendor ID: 0x${device.vendorId.toString(16).padStart(4, '0')}`)
        addDebugLog(`📋 Product ID: 0x${device.productId.toString(16).padStart(4, '0')}`)
        addDebugLog(`📋 Serial: ${device.serialNumber || 'N/A'}`)
        
        showSuccessToast(`Found: ${device.manufacturerName || 'Unknown'} ${device.productName || 'Device'}`)
        
        // Try to open the device for more info
        try {
          await device.open()
          addDebugLog('✅ Device opened successfully')
          addDebugLog(`📋 Configuration: ${device.configuration?.configurationValue || 'N/A'}`)
          await device.close()
          addDebugLog('✅ Device closed')
        } catch (openError) {
          addDebugLog(`⚠️ Could not open device (normal for some devices): ${openError}`)
        }
      } else {
        addDebugLog('❌ No device was selected')
      }
    } catch (error) {
      if (error instanceof Error) {
        if (error.name === 'NotFoundError') {
          addDebugLog('ℹ️ User cancelled or no compatible devices found')
          showErrorToast('No device selected or no compatible devices available')
        } else if (error.name === 'SecurityError') {
          addDebugLog('🔒 Security error - check HTTPS and permissions')
          showErrorToast('Security error - check browser permissions')
        } else {
          addDebugLog(`❌ Error: ${error.name} - ${error.message}`)
          showErrorToast(`USB error: ${error.message}`)
        }
      } else {
        addDebugLog(`❌ Unknown error: ${error}`)
        showErrorToast('Unknown USB error')
      }
    } finally {
      setIsLoading(false)
    }
  }

  const checkPermissions = () => {
    addDebugLog('🔍 Checking browser environment...')
    addDebugLog(`📍 URL: ${window.location.href}`)
    addDebugLog(`🔒 Protocol: ${window.location.protocol}`)
    addDebugLog(`🌐 User Agent: ${navigator.userAgent}`)
    
    if (window.location.protocol !== 'https:' && window.location.hostname !== 'localhost') {
      addDebugLog('⚠️ Not using HTTPS - WebUSB requires HTTPS in production')
    } else {
      addDebugLog('✅ Using secure connection')
    }
  }

  return (
    <Card sx={{ p: 3, mb: 4, bg: 'background', border: '1px solid', borderColor: 'muted' }}>
      <Text variant="subheading" sx={{ mb: 3, fontWeight: 'bold', color: 'primary' }}>
        🔧 USB Debug Panel
      </Text>
      
      <Text sx={{ mb: 3, fontSize: 1, color: 'muted' }}>
        Use this panel to debug USB device discovery issues. Check the console for detailed logs.
      </Text>

      <Flex sx={{ gap: 2, mb: 3, flexWrap: 'wrap' }}>
        <ThemeButton
          text="Check WebUSB Support"
          variant="secondary"
          onClick={checkWebUSBSupport}
        />
        <ThemeButton
          text="Check Permissions"
          variant="secondary"
          onClick={checkPermissions}
        />
        <ThemeButton
          text="Get Paired Devices"
          variant="secondary"
          onClick={getPairedDevices}
        />
        <ThemeButton
          text="🔌 Request USB Device"
          variant="primary"
          onClick={requestUSBDevice}
          disabled={isLoading}
        />
        <ThemeButton
          text="Clear Logs"
          variant="danger"
          onClick={clearLogs}
        />
      </Flex>

      {debugInfo.length > 0 && (
        <Box
          sx={{
            bg: 'muted',
            p: 2,
            borderRadius: 1,
            maxHeight: '300px',
            overflowY: 'auto',
            fontFamily: 'monospace',
            fontSize: 0,
          }}
        >
          {debugInfo.map((log, index) => (
            <Text
              key={index}
              sx={{
                display: 'block',
                mb: 1,
                color: log.includes('❌') ? 'red' : 
                       log.includes('✅') ? 'green' : 
                       log.includes('⚠️') ? 'orange' : 
                       log.includes('🎉') ? 'blue' : 'text'
              }}
            >
              {log}
            </Text>
          ))}
        </Box>
      )}

      <Box sx={{ mt: 3, p: 2, bg: 'muted', borderRadius: 1 }}>
        <Text sx={{ fontSize: 0, fontWeight: 'bold', mb: 1 }}>
          💡 Troubleshooting Tips:
        </Text>
        <Text sx={{ fontSize: 0, display: 'block', mb: 1 }}>
          1. Make sure you're using Chrome or Edge browser
        </Text>
        <Text sx={{ fontSize: 0, display: 'block', mb: 1 }}>
          2. Ensure you're on HTTPS (or localhost for development)
        </Text>
        <Text sx={{ fontSize: 0, display: 'block', mb: 1 }}>
          3. Check that USB devices are properly connected
        </Text>
        <Text sx={{ fontSize: 0, display: 'block', mb: 1 }}>
          4. Try the "Request USB Device" button - it should open a browser dialog
        </Text>
        <Text sx={{ fontSize: 0, display: 'block' }}>
          5. Look for your mouse/keyboard in the device list that appears
        </Text>
      </Box>
    </Card>
  )
}

export default USBDebugPanel
