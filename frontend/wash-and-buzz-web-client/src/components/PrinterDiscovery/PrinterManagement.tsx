'use client'

import React, { useState, useCallback, useEffect } from 'react'
import { Box, Flex, Text, Card, Button } from 'theme-ui'
import { ThemeButton } from '@/components/core/Button/Button'
import { useDispatch, useSelector } from 'react-redux'
import {
  DiscoveredPrinter,
  PrinterDiscoveryStatus,
  PrinterData,
} from '@/types/module/printersModule'
import { MainStoreType } from '@/types/store/reducers/main.reducers'
import { addPrinter, updatePrinterData } from '@/store/actions/printers.action'
import {
  showSuccessToast,
  showErrorToast,
  showDefaultToast,
} from '@/utils/toastUtils'
// Dynamic import to prevent SSR issues
// import PrinterDiscoveryService from '@/services/printerDiscovery.service'

interface PrinterManagementProps {
  discoveredPrinters: DiscoveredPrinter[]
  onPrinterUpdated?: (printer: DiscoveredPrinter) => void
  onClose?: () => void
}

const PrinterManagement: React.FC<PrinterManagementProps> = ({
  discoveredPrinters,
  onPrinterUpdated,
  onClose,
}) => {
  const dispatch = useDispatch()
  const storeData = useSelector((state: MainStoreType) => state?.storeData)
  const existingPrinters = useSelector(
    (state: MainStoreType) => state?.printersData?.data || []
  )

  const [selectedPrinters, setSelectedPrinters] = useState<Set<string>>(
    new Set()
  )
  const [connectingPrinters, setConnectingPrinters] = useState<Set<string>>(
    new Set()
  )
  const [testingPrinters, setTestingPrinters] = useState<Set<string>>(new Set())
  const [printerDiscoveryService, setPrinterDiscoveryService] =
    useState<any>(null)

  // Initialize printer discovery service on client side
  useEffect(() => {
    const initService = async () => {
      try {
        const { default: PrinterDiscoveryService } = await import(
          '@/services/printerDiscovery.service'
        )
        setPrinterDiscoveryService(PrinterDiscoveryService)
      } catch (error) {
        console.error('Failed to load printer discovery service:', error)
      }
    }
    initService()
  }, [])

  // Check which discovered printers are already in the system
  const getExistingPrinter = useCallback(
    (discoveredPrinter: DiscoveredPrinter): PrinterData | null => {
      return (
        existingPrinters.find(
          (existing) =>
            existing.macAddress ===
              discoveredPrinter.connectionDetails.macAddress ||
            existing.name === discoveredPrinter.name
        ) || null
      )
    },
    [existingPrinters]
  )

  // Handle printer selection
  const handlePrinterSelect = useCallback((printerId: string) => {
    setSelectedPrinters((prev) => {
      const newSet = new Set(prev)
      if (newSet.has(printerId)) {
        newSet.delete(printerId)
      } else {
        newSet.add(printerId)
      }
      return newSet
    })
  }, [])

  // Test printer connection
  const handleTestConnection = useCallback(
    async (printer: DiscoveredPrinter) => {
      if (!printerDiscoveryService) {
        showErrorToast('Printer discovery service not ready')
        return
      }

      setTestingPrinters((prev) => new Set(prev).add(printer.id))

      try {
        showDefaultToast('Testing printer connection...')

        const testData = {
          storeName: storeData?.data?.name || 'Test Store',
          storeAddress: storeData?.data?.address || 'Test Address',
          testPattern: false,
          includeBarcode: false,
          includeQRCode: false,
          customText: 'Connection test successful!',
        }

        const result = await printerDiscoveryService.testPrint(
          printer,
          testData
        )

        if (result.success) {
          showSuccessToast('Printer connection test successful!')
          if (onPrinterUpdated) {
            onPrinterUpdated({
              ...printer,
              status: PrinterDiscoveryStatus.TEST_SUCCESS,
            })
          }
        } else {
          showErrorToast(
            `Connection test failed: ${result.error || 'Unknown error'}`
          )
          if (onPrinterUpdated) {
            onPrinterUpdated({
              ...printer,
              status: PrinterDiscoveryStatus.TEST_FAILED,
            })
          }
        }
      } catch (error) {
        console.error('Connection test error:', error)
        showErrorToast('Failed to test printer connection')
      } finally {
        setTestingPrinters((prev) => {
          const newSet = new Set(prev)
          newSet.delete(printer.id)
          return newSet
        })
      }
    },
    [storeData, printerDiscoveryService, onPrinterUpdated]
  )

  // Add printer to system
  const handleAddPrinter = useCallback(
    async (printer: DiscoveredPrinter) => {
      if (!storeData?.data?.id) {
        showErrorToast('Store information not available')
        return
      }

      setConnectingPrinters((prev) => new Set(prev).add(printer.id))

      try {
        const printerData: PrinterData = {
          storeId: storeData.data.id,
          name: printer.name,
          macAddress: printer.connectionDetails.macAddress || printer.id,
          username: 'admin', // Default username
          usage: [
            {
              id: 1,
              name: 'Receipt Printing',
              enabled: true,
              printOptions: ['PRINT_ALL'],
            },
          ],
        }

        // Add printer via Redux action
        await new Promise<void>((resolve, reject) => {
          dispatch(
            addPrinter(printerData, (success: boolean, error?: string) => {
              if (success) {
                resolve()
              } else {
                reject(new Error(error || 'Failed to add printer'))
              }
            })
          )
        })

        showSuccessToast(`Printer "${printer.name}" added successfully!`)

        if (onPrinterUpdated) {
          onPrinterUpdated({
            ...printer,
            status: PrinterDiscoveryStatus.CONNECTED,
          })
        }
      } catch (error) {
        console.error('Add printer error:', error)
        showErrorToast(
          `Failed to add printer: ${error instanceof Error ? error.message : 'Unknown error'}`
        )
      } finally {
        setConnectingPrinters((prev) => {
          const newSet = new Set(prev)
          newSet.delete(printer.id)
          return newSet
        })
      }
    },
    [storeData, dispatch, onPrinterUpdated]
  )

  // Add multiple selected printers
  const handleAddSelectedPrinters = useCallback(async () => {
    const printersToAdd = discoveredPrinters.filter((p) =>
      selectedPrinters.has(p.id)
    )

    if (printersToAdd.length === 0) {
      showErrorToast('No printers selected')
      return
    }

    showDefaultToast(`Adding ${printersToAdd.length} printer(s)...`)

    let successCount = 0
    let errorCount = 0

    for (const printer of printersToAdd) {
      try {
        await handleAddPrinter(printer)
        successCount++
      } catch (error) {
        errorCount++
      }
    }

    if (successCount > 0) {
      showSuccessToast(`Successfully added ${successCount} printer(s)`)
    }
    if (errorCount > 0) {
      showErrorToast(`Failed to add ${errorCount} printer(s)`)
    }

    setSelectedPrinters(new Set())
  }, [discoveredPrinters, selectedPrinters, handleAddPrinter])

  // Get printer status display
  const getPrinterStatusDisplay = useCallback(
    (printer: DiscoveredPrinter) => {
      const existing = getExistingPrinter(printer)

      if (existing) {
        return {
          status: 'Already Added',
          color: 'green',
          canAdd: false,
          canTest: true,
        }
      }

      if (connectingPrinters.has(printer.id)) {
        return {
          status: 'Adding...',
          color: 'orange',
          canAdd: false,
          canTest: false,
        }
      }

      if (testingPrinters.has(printer.id)) {
        return {
          status: 'Testing...',
          color: 'purple',
          canAdd: true,
          canTest: false,
        }
      }

      switch (printer.status) {
        case PrinterDiscoveryStatus.TEST_SUCCESS:
          return {
            status: 'Test Passed',
            color: 'green',
            canAdd: true,
            canTest: true,
          }
        case PrinterDiscoveryStatus.TEST_FAILED:
          return {
            status: 'Test Failed',
            color: 'red',
            canAdd: true,
            canTest: true,
          }
        case PrinterDiscoveryStatus.CONNECTED:
          return {
            status: 'Connected',
            color: 'green',
            canAdd: false,
            canTest: true,
          }
        default:
          return {
            status: 'Ready',
            color: 'blue',
            canAdd: true,
            canTest: true,
          }
      }
    },
    [getExistingPrinter, connectingPrinters, testingPrinters]
  )

  if (discoveredPrinters.length === 0) {
    return (
      <Card sx={{ p: 4, textAlign: 'center' }}>
        <Text sx={{ fontSize: 3, mb: 2 }}>🔍</Text>
        <Text sx={{ color: 'muted' }}>No printers discovered yet</Text>
        <Text sx={{ fontSize: 1, color: 'muted', mt: 2 }}>
          Use the discovery feature to find printers on your network
        </Text>
      </Card>
    )
  }

  return (
    <Box>
      {/* Header */}
      <Flex
        sx={{ justifyContent: 'space-between', alignItems: 'center', mb: 4 }}
      >
        <Text variant="heading" sx={{ fontSize: 3, fontWeight: 'bold' }}>
          Printer Management ({discoveredPrinters.length})
        </Text>
        <Flex sx={{ gap: 2 }}>
          {selectedPrinters.size > 0 && (
            <ThemeButton
              text={`Add Selected (${selectedPrinters.size})`}
              variant="primary"
              onClick={handleAddSelectedPrinters}
            />
          )}
          {onClose && (
            <ThemeButton text="Close" variant="secondary" onClick={onClose} />
          )}
        </Flex>
      </Flex>

      {/* Printer List */}
      <Box sx={{ display: 'grid', gap: 3 }}>
        {discoveredPrinters.map((printer) => {
          const statusInfo = getPrinterStatusDisplay(printer)
          const isSelected = selectedPrinters.has(printer.id)
          const existing = getExistingPrinter(printer)

          return (
            <Card
              key={printer.id}
              sx={{
                p: 3,
                border: '2px solid',
                borderColor: isSelected ? 'primary' : 'muted',
                bg: isSelected ? 'primary' : 'background',
                color: isSelected ? 'white' : 'text',
                cursor: statusInfo.canAdd ? 'pointer' : 'default',
                opacity: existing ? 0.7 : 1,
                transition: 'all 0.2s ease',
                '&:hover': statusInfo.canAdd
                  ? {
                      borderColor: 'primary',
                      transform: 'translateY(-1px)',
                    }
                  : {},
              }}
              onClick={() =>
                statusInfo.canAdd && handlePrinterSelect(printer.id)
              }
            >
              <Flex
                sx={{ alignItems: 'center', justifyContent: 'space-between' }}
              >
                {/* Printer Info */}
                <Box sx={{ flex: 1 }}>
                  <Flex sx={{ alignItems: 'center', mb: 2 }}>
                    <Text sx={{ fontSize: 2, mr: 2 }}>
                      {printer.connectionMethod === 'USB'
                        ? '🔌'
                        : printer.connectionMethod === 'BLUETOOTH'
                          ? '📶'
                          : '🌐'}
                    </Text>
                    <Box>
                      <Text sx={{ fontWeight: 'bold', fontSize: 2 }}>
                        {printer.name}
                      </Text>
                      <Text sx={{ fontSize: 1, opacity: 0.8 }}>
                        {printer.manufacturer} • {printer.connectionMethod}
                      </Text>
                    </Box>
                    {isSelected && statusInfo.canAdd && (
                      <Text sx={{ ml: 2, fontSize: 2 }}>✓</Text>
                    )}
                  </Flex>

                  <Flex sx={{ gap: 2, alignItems: 'center' }}>
                    <Text
                      sx={{
                        fontSize: 0,
                        px: 2,
                        py: 1,
                        borderRadius: 1,
                        bg: statusInfo.color,
                        color: 'white',
                        fontWeight: 'bold',
                      }}
                    >
                      {statusInfo.status}
                    </Text>

                    {printer.signal && (
                      <Text sx={{ fontSize: 0 }}>
                        Signal: {printer.signal}%
                      </Text>
                    )}

                    {existing && (
                      <Text sx={{ fontSize: 0, fontStyle: 'italic' }}>
                        (Already in system)
                      </Text>
                    )}
                  </Flex>
                </Box>

                {/* Actions */}
                <Flex sx={{ gap: 2, ml: 3 }}>
                  {statusInfo.canTest && (
                    <ThemeButton
                      text="Test"
                      variant="secondary"
                      size="small"
                      onClick={(e) => {
                        e.stopPropagation()
                        handleTestConnection(printer)
                      }}
                      disabled={testingPrinters.has(printer.id)}
                    />
                  )}

                  {statusInfo.canAdd && !existing && (
                    <ThemeButton
                      text="Add"
                      variant="primary"
                      size="small"
                      onClick={(e) => {
                        e.stopPropagation()
                        handleAddPrinter(printer)
                      }}
                      disabled={connectingPrinters.has(printer.id)}
                    />
                  )}
                </Flex>
              </Flex>
            </Card>
          )
        })}
      </Box>

      {/* Summary */}
      <Box sx={{ mt: 4, p: 3, bg: 'muted', borderRadius: 1 }}>
        <Text sx={{ fontSize: 1 }}>
          Total: {discoveredPrinters.length} • Selected: {selectedPrinters.size}{' '}
          • Already Added:{' '}
          {discoveredPrinters.filter((p) => getExistingPrinter(p)).length}
        </Text>
      </Box>
    </Box>
  )
}

export default PrinterManagement
