'use client'

import React from 'react'
import { Box, Flex, Text, Card } from 'theme-ui'
import { PrinterConnectionMethod } from '@/types/module/printersModule'
import { ThemeButton } from '@/components/core/Button/Button'

interface ConnectionMethodSelectorProps {
  selectedMethods: PrinterConnectionMethod[]
  onMethodToggle: (method: PrinterConnectionMethod) => void
  disabled?: boolean
}

interface ConnectionMethodInfo {
  method: PrinterConnectionMethod
  label: string
  description: string
  icon: string
  requirements?: string[]
  supported: boolean
}

const ConnectionMethodSelector: React.FC<ConnectionMethodSelectorProps> = ({
  selectedMethods,
  onMethodToggle,
  disabled = false,
}) => {
  // Check browser capabilities
  const hasWebUSB = typeof navigator !== 'undefined' && 'usb' in navigator
  const hasWebBluetooth = typeof navigator !== 'undefined' && 'bluetooth' in navigator
  const hasNetworkAccess = typeof navigator !== 'undefined' && 'onLine' in navigator

  const connectionMethods: ConnectionMethodInfo[] = [
    {
      method: PrinterConnectionMethod.NETWORK,
      label: 'Network Scan',
      description: 'Scan local network for Epson printers',
      icon: '🌐',
      requirements: ['Network access', 'Same network as printer'],
      supported: hasNetworkAccess,
    },
    {
      method: PrinterConnectionMethod.USB,
      label: 'USB Connection',
      description: 'Connect to USB-attached printers',
      icon: '🔌',
      requirements: ['WebUSB support', 'USB printer connected'],
      supported: hasWebUSB,
    },
    {
      method: PrinterConnectionMethod.BLUETOOTH,
      label: 'Bluetooth',
      description: 'Connect to Bluetooth-enabled printers',
      icon: '📶',
      requirements: ['Web Bluetooth support', 'Bluetooth enabled'],
      supported: hasWebBluetooth,
    },
    {
      method: PrinterConnectionMethod.ETHERNET,
      label: 'Ethernet',
      description: 'Direct ethernet connection scanning',
      icon: '🔗',
      requirements: ['Network access', 'Ethernet connection'],
      supported: hasNetworkAccess,
    },
  ]

  const isMethodSelected = (method: PrinterConnectionMethod) => {
    return selectedMethods.includes(method)
  }

  const getMethodButtonVariant = (method: ConnectionMethodInfo) => {
    if (!method.supported) return 'disabled'
    if (isMethodSelected(method.method)) return 'primary'
    return 'secondary'
  }

  const handleMethodClick = (method: ConnectionMethodInfo) => {
    if (!method.supported || disabled) return
    onMethodToggle(method.method)
  }

  return (
    <Box>
      <Flex sx={{ flexWrap: 'wrap', gap: 3 }}>
        {connectionMethods.map((method) => (
          <Card
            key={method.method}
            sx={{
              flex: '1 1 280px',
              minWidth: '280px',
              p: 3,
              cursor: method.supported && !disabled ? 'pointer' : 'not-allowed',
              border: '2px solid',
              borderColor: isMethodSelected(method.method) ? 'primary' : 'muted',
              bg: method.supported 
                ? isMethodSelected(method.method) 
                  ? 'primary' 
                  : 'background'
                : 'muted',
              color: isMethodSelected(method.method) ? 'white' : 'text',
              opacity: method.supported ? 1 : 0.6,
              transition: 'all 0.2s ease',
              '&:hover': method.supported && !disabled ? {
                borderColor: 'primary',
                transform: 'translateY(-2px)',
                boxShadow: '0 4px 8px rgba(0,0,0,0.1)',
              } : {},
            }}
            onClick={() => handleMethodClick(method)}
          >
            {/* Method Header */}
            <Flex sx={{ alignItems: 'center', mb: 2 }}>
              <Text sx={{ fontSize: 4, mr: 2 }}>{method.icon}</Text>
              <Box sx={{ flex: 1 }}>
                <Text 
                  sx={{ 
                    fontWeight: 'bold', 
                    fontSize: 2,
                    color: isMethodSelected(method.method) ? 'white' : 'text'
                  }}
                >
                  {method.label}
                </Text>
                {!method.supported && (
                  <Text 
                    sx={{ 
                      fontSize: 0, 
                      color: 'orange',
                      fontWeight: 'bold'
                    }}
                  >
                    Not Supported
                  </Text>
                )}
              </Box>
              {isMethodSelected(method.method) && (
                <Text sx={{ fontSize: 3, color: 'white' }}>✓</Text>
              )}
            </Flex>

            {/* Method Description */}
            <Text 
              sx={{ 
                fontSize: 1, 
                mb: 2,
                color: isMethodSelected(method.method) ? 'white' : 'text'
              }}
            >
              {method.description}
            </Text>

            {/* Requirements */}
            {method.requirements && (
              <Box>
                <Text 
                  sx={{ 
                    fontSize: 0, 
                    fontWeight: 'bold', 
                    mb: 1,
                    color: isMethodSelected(method.method) ? 'white' : 'text'
                  }}
                >
                  Requirements:
                </Text>
                <Box as="ul" sx={{ m: 0, pl: 3, fontSize: 0 }}>
                  {method.requirements.map((req, index) => (
                    <li 
                      key={index}
                      style={{ 
                        color: isMethodSelected(method.method) ? 'white' : undefined
                      }}
                    >
                      {req}
                    </li>
                  ))}
                </Box>
              </Box>
            )}

            {/* Browser Support Status */}
            <Box sx={{ mt: 2, pt: 2, borderTop: '1px solid', borderColor: 'muted' }}>
              <Flex sx={{ alignItems: 'center', justifyContent: 'space-between' }}>
                <Text 
                  sx={{ 
                    fontSize: 0,
                    color: isMethodSelected(method.method) ? 'white' : 'text'
                  }}
                >
                  Browser Support:
                </Text>
                <Text 
                  sx={{ 
                    fontSize: 0,
                    fontWeight: 'bold',
                    color: method.supported 
                      ? isMethodSelected(method.method) ? 'white' : 'green'
                      : 'red'
                  }}
                >
                  {method.supported ? '✓ Available' : '✗ Unavailable'}
                </Text>
              </Flex>
            </Box>
          </Card>
        ))}
      </Flex>

      {/* Selection Summary */}
      <Box sx={{ mt: 3, p: 2, bg: 'muted', borderRadius: 1 }}>
        <Flex sx={{ alignItems: 'center', justifyContent: 'space-between' }}>
          <Text sx={{ fontSize: 1, fontWeight: 'bold' }}>
            Selected Methods: {selectedMethods.length}
          </Text>
          {selectedMethods.length > 0 && (
            <Text sx={{ fontSize: 1 }}>
              {selectedMethods.join(', ')}
            </Text>
          )}
        </Flex>
        
        {selectedMethods.length === 0 && (
          <Text sx={{ fontSize: 1, color: 'orange', mt: 1 }}>
            Please select at least one connection method to start discovery
          </Text>
        )}
      </Box>

      {/* Browser Compatibility Info */}
      <Box sx={{ mt: 3, p: 2, bg: 'background', border: '1px solid', borderColor: 'muted', borderRadius: 1 }}>
        <Text sx={{ fontSize: 1, fontWeight: 'bold', mb: 2 }}>
          Browser Compatibility:
        </Text>
        <Box sx={{ fontSize: 0, lineHeight: 1.4 }}>
          <Text sx={{ display: 'block', mb: 1 }}>
            <strong>Network/Ethernet:</strong> Supported in all modern browsers
          </Text>
          <Text sx={{ display: 'block', mb: 1 }}>
            <strong>USB:</strong> Requires Chrome/Edge with WebUSB support
          </Text>
          <Text sx={{ display: 'block', mb: 1 }}>
            <strong>Bluetooth:</strong> Requires Chrome/Edge with Web Bluetooth support
          </Text>
          <Text sx={{ display: 'block', color: 'orange' }}>
            <strong>Note:</strong> Some features may require HTTPS in production
          </Text>
        </Box>
      </Box>
    </Box>
  )
}

export default ConnectionMethodSelector
