'use client'

import React, { useState, useEffect } from 'react'
import { Box, Flex, Text, Card } from 'theme-ui'
import { PrinterConnectionMethod } from '@/types/module/printersModule'
import { ThemeButton } from '@/components/core/Button/Button'

interface ConnectionMethodSelectorProps {
  selectedMethods: PrinterConnectionMethod[]
  onMethodToggle: (method: PrinterConnectionMethod) => void
  disabled?: boolean
}

interface ConnectionMethodInfo {
  method: PrinterConnectionMethod
  label: string
  description: string
  icon: string
  requirements?: string[]
  supported: boolean
}

const ConnectionMethodSelector: React.FC<ConnectionMethodSelectorProps> = ({
  selectedMethods,
  onMethodToggle,
  disabled = false,
}) => {
  // State for browser capabilities to prevent hydration mismatch
  const [browserCapabilities, setBrowserCapabilities] = useState({
    hasWebUSB: false,
    hasWebBluetooth: false,
    hasNetworkAccess: false,
    isLoading: true,
  })

  // Check browser capabilities on client side only
  useEffect(() => {
    const checkCapabilities = async () => {
      // Add a small delay to ensure proper hydration
      await new Promise((resolve) => setTimeout(resolve, 100))

      const capabilities = {
        hasWebUSB: typeof navigator !== 'undefined' && 'usb' in navigator,
        hasWebBluetooth:
          typeof navigator !== 'undefined' && 'bluetooth' in navigator,
        hasNetworkAccess:
          typeof navigator !== 'undefined' && 'onLine' in navigator,
        isLoading: false,
      }

      setBrowserCapabilities(capabilities)
    }

    checkCapabilities()
  }, [])

  const connectionMethods: ConnectionMethodInfo[] = [
    {
      method: PrinterConnectionMethod.NETWORK,
      label: 'Network Discovery',
      description:
        'Scan local network for all available devices (printers, scanners, IoT devices)',
      icon: '🌐',
      requirements: ['Network access', 'Same network as devices'],
      supported: browserCapabilities.hasNetworkAccess,
    },
    {
      method: PrinterConnectionMethod.USB,
      label: 'USB Device Discovery',
      description:
        'Discover all USB-connected devices (printers, scanners, storage, etc.)',
      icon: '🔌',
      requirements: ['WebUSB support', 'USB devices connected'],
      supported: browserCapabilities.hasWebUSB,
    },
    {
      method: PrinterConnectionMethod.BLUETOOTH,
      label: 'Bluetooth Discovery',
      description:
        'Find all Bluetooth devices (printers, speakers, keyboards, mice, etc.)',
      icon: '📶',
      requirements: ['Web Bluetooth support', 'Bluetooth enabled'],
      supported: browserCapabilities.hasWebBluetooth,
    },
    {
      method: PrinterConnectionMethod.ETHERNET,
      label: 'Ethernet Discovery',
      description:
        'Direct ethernet device scanning (network printers, servers, etc.)',
      icon: '🔗',
      requirements: ['Network access', 'Ethernet connection'],
      supported: browserCapabilities.hasNetworkAccess,
    },
  ]

  const isMethodSelected = (method: PrinterConnectionMethod) => {
    return selectedMethods.includes(method)
  }

  const getMethodButtonVariant = (method: ConnectionMethodInfo) => {
    if (!method.supported) return 'disabled'
    if (isMethodSelected(method.method)) return 'primary'
    return 'secondary'
  }

  const handleMethodClick = (method: ConnectionMethodInfo) => {
    if (!method.supported || disabled) return
    onMethodToggle(method.method)
  }

  return (
    <Box>
      <Flex sx={{ flexWrap: 'wrap', gap: 3 }}>
        {connectionMethods.map((method) => (
          <Card
            key={method.method}
            sx={{
              flex: '1 1 280px',
              minWidth: '280px',
              p: 3,
              cursor: method.supported && !disabled ? 'pointer' : 'not-allowed',
              border: '2px solid',
              borderColor: isMethodSelected(method.method)
                ? 'primary'
                : 'muted',
              bg: method.supported
                ? isMethodSelected(method.method)
                  ? 'primary'
                  : 'background'
                : 'muted',
              color: isMethodSelected(method.method) ? 'white' : 'text',
              opacity: method.supported ? 1 : 0.6,
              transition: 'all 0.2s ease',
              '&:hover':
                method.supported && !disabled
                  ? {
                      borderColor: 'primary',
                      transform: 'translateY(-2px)',
                      boxShadow: '0 4px 8px rgba(0,0,0,0.1)',
                    }
                  : {},
            }}
            onClick={() => handleMethodClick(method)}
          >
            {/* Method Header */}
            <Flex sx={{ alignItems: 'center', mb: 2 }}>
              <Text sx={{ fontSize: 4, mr: 2 }}>{method.icon}</Text>
              <Box sx={{ flex: 1 }}>
                <Text
                  sx={{
                    fontWeight: 'bold',
                    fontSize: 2,
                    color: isMethodSelected(method.method) ? 'white' : 'text',
                  }}
                >
                  {method.label}
                </Text>
                {browserCapabilities.isLoading ? (
                  <Text
                    sx={{
                      fontSize: 0,
                      color: 'blue',
                      fontWeight: 'bold',
                    }}
                  >
                    Checking Support...
                  </Text>
                ) : !method.supported ? (
                  <Text
                    sx={{
                      fontSize: 0,
                      color: 'orange',
                      fontWeight: 'bold',
                    }}
                  >
                    Not Available
                  </Text>
                ) : (
                  <Text
                    sx={{
                      fontSize: 0,
                      color: 'green',
                      fontWeight: 'bold',
                    }}
                  >
                    Available
                  </Text>
                )}
              </Box>
              {isMethodSelected(method.method) && (
                <Text sx={{ fontSize: 3, color: 'white' }}>✓</Text>
              )}
            </Flex>

            {/* Method Description */}
            <Text
              sx={{
                fontSize: 1,
                mb: 2,
                color: isMethodSelected(method.method) ? 'white' : 'text',
              }}
            >
              {method.description}
            </Text>

            {/* Requirements */}
            {method.requirements && (
              <Box>
                <Text
                  sx={{
                    fontSize: 0,
                    fontWeight: 'bold',
                    mb: 1,
                    color: isMethodSelected(method.method) ? 'white' : 'text',
                  }}
                >
                  Requirements:
                </Text>
                <Box as="ul" sx={{ m: 0, pl: 3, fontSize: 0 }}>
                  {method.requirements.map((req, index) => (
                    <li
                      key={index}
                      style={{
                        color: isMethodSelected(method.method)
                          ? 'white'
                          : undefined,
                      }}
                    >
                      {req}
                    </li>
                  ))}
                </Box>
              </Box>
            )}

            {/* Browser Support Status */}
            <Box
              sx={{
                mt: 2,
                pt: 2,
                borderTop: '1px solid',
                borderColor: 'muted',
              }}
            >
              <Flex
                sx={{ alignItems: 'center', justifyContent: 'space-between' }}
              >
                <Text
                  sx={{
                    fontSize: 0,
                    color: isMethodSelected(method.method) ? 'white' : 'text',
                  }}
                >
                  Browser Support:
                </Text>
                {browserCapabilities.isLoading ? (
                  <Text
                    sx={{
                      fontSize: 0,
                      fontWeight: 'bold',
                      color: 'blue',
                    }}
                  >
                    🔄 Checking...
                  </Text>
                ) : (
                  <Text
                    sx={{
                      fontSize: 0,
                      fontWeight: 'bold',
                      color: method.supported
                        ? isMethodSelected(method.method)
                          ? 'white'
                          : 'green'
                        : 'red',
                    }}
                  >
                    {method.supported ? '✓ Available' : '✗ Not Available'}
                  </Text>
                )}
              </Flex>

              {/* Additional capability info */}
              {!browserCapabilities.isLoading && (
                <Box sx={{ mt: 1 }}>
                  <Text
                    sx={{
                      fontSize: 0,
                      color: isMethodSelected(method.method)
                        ? 'white'
                        : 'muted',
                      fontStyle: 'italic',
                    }}
                  >
                    {method.supported
                      ? `Ready to discover ${method.method.toLowerCase()} devices`
                      : `${method.method} discovery not supported in this browser`}
                  </Text>
                </Box>
              )}
            </Box>
          </Card>
        ))}
      </Flex>

      {/* Selection Summary */}
      <Box sx={{ mt: 3, p: 2, bg: 'muted', borderRadius: 1 }}>
        <Flex sx={{ alignItems: 'center', justifyContent: 'space-between' }}>
          <Text sx={{ fontSize: 1, fontWeight: 'bold' }}>
            Selected Methods: {selectedMethods.length}
          </Text>
          {selectedMethods.length > 0 && (
            <Text sx={{ fontSize: 1 }}>{selectedMethods.join(', ')}</Text>
          )}
        </Flex>

        {selectedMethods.length === 0 && (
          <Text sx={{ fontSize: 1, color: 'orange', mt: 1 }}>
            Please select at least one connection method to start discovery
          </Text>
        )}
      </Box>

      {/* Browser Compatibility Info */}
      <Box
        sx={{
          mt: 3,
          p: 2,
          bg: 'background',
          border: '1px solid',
          borderColor: 'muted',
          borderRadius: 1,
        }}
      >
        <Text sx={{ fontSize: 1, fontWeight: 'bold', mb: 2 }}>
          Browser Compatibility & Device Support:
        </Text>
        <Box sx={{ fontSize: 0, lineHeight: 1.4 }}>
          <Text sx={{ display: 'block', mb: 1 }}>
            <strong>Network/Ethernet:</strong> ✅ All modern browsers -
            Discovers printers, scanners, IoT devices
          </Text>
          <Text sx={{ display: 'block', mb: 1 }}>
            <strong>USB Discovery:</strong> ✅ Chrome/Edge with WebUSB - All USB
            devices (storage, input, printers, etc.)
          </Text>
          <Text sx={{ display: 'block', mb: 1 }}>
            <strong>Bluetooth Discovery:</strong> ✅ Chrome/Edge with Web
            Bluetooth - Audio, input, and printer devices
          </Text>
          <Text sx={{ display: 'block', mb: 2, color: 'blue' }}>
            <strong>Device Types:</strong> Printers, Scanners, Keyboards, Mice,
            Storage, Audio, Network Adapters
          </Text>
          <Text sx={{ display: 'block', color: 'orange' }}>
            <strong>Note:</strong> Device APIs require HTTPS in production •
            Some devices may need manual pairing
          </Text>
        </Box>
      </Box>
    </Box>
  )
}

export default ConnectionMethodSelector
