'use client'

import { ThemeButton } from '../../core/Button/Button'
import addIcon from '../../../../public/images/add.svg'
import { Flex, Paragraph, Text } from 'theme-ui'
import { translation } from '@/utils/translation'
import React, { useRef, useState } from 'react'
import { AddPrinterModal } from '../../core/PopupModals/AddPrinterModal'
import { useDispatch } from 'react-redux'
import { useEffect } from 'react'
import { getPrintersData } from '@/store/actions/printers.action'
import { PrintersDataState } from '@/types/store/reducers/printers.reducers'
import { useSelector } from 'react-redux'
import { MainStoreType } from '@/types/store/reducers/main.reducers'
import { PrinterData } from '@/types/module/printersModule'
import { DeletePrintersModal } from '../../core/PopupModals/DeletePrintersModal'
import { generateUniqueAlphanumeric } from '@/utils/strings'
import MqttDisabledPrinters from './mqttDisabledPrinters'
import MqttEnabledPrinters from './mqttEnabledPrinters'

const Printers: React.FC = () => {
  const dispatch = useDispatch()
  const [openModal, setOpenModal] = useState<boolean>(false)
  const [deletePrinter, setDeletePrinter] = useState<PrinterData>()
  const [isDeleteItem, setIsDeleteItem] = useState(false)
  const [isDropdownOpen, setIsDropdownOpen] = useState<boolean>(false)
  const [isPrinterEdit, setIsPrinterEdit] = useState<boolean>(false)
  const [printerEditData, setPrinterEditData] = useState<PrinterData | null>()

  const printers: PrintersDataState = useSelector(
    (state: MainStoreType) => state?.printersData
  )
  const storeData = useSelector((state: MainStoreType) => state?.storeData)

  const printerRef = useRef(false)
  useEffect(() => {
    if (!printerRef.current) {
      dispatch(getPrintersData())
    }
  }, [dispatch])

  const removePrintersHandler = (printer: PrinterData) => {
    setDeletePrinter(printer)
    setIsDeleteItem(true)
  }
  return (
    <div
      className={`vh-100 px-20 custom-scroll printer-dropdown ${isDropdownOpen ? 'overflow-hidden' : ''}`}
    >
      <Flex className="align-items-center pl-10 py-30">
        <Text
          variant="Primary28demi107"
          as={'p'}
          sx={{ fontSize: '25px', fontWeight: 600 }}
        >
          {translation?.MANAGER_PRINTERS}
        </Text>
        <Flex sx={{ marginLeft: 'auto', gap: 2 }}>
          <ThemeButton
            className="cta-button"
            text="Discover Printers"
            variant="secondary"
            onClick={() =>
              window.open('/settings/hardware/printer-discovery', '_blank')
            }
          />
          <ThemeButton
            className="cta-button"
            text={translation?.ADD_PRINTER}
            icon={addIcon}
            onClick={() => {
              setPrinterEditData(null)
              setIsPrinterEdit(false)
              setOpenModal(true)
            }}
          />
        </Flex>
      </Flex>
      <div className="printer-list-height overflow-y-auto px-20">
        {storeData?.data?.id !== null &&
        storeData?.data?.id !== undefined &&
        printers?.data &&
        printers?.data?.length > 0 ? (
          <>
            <Text
              variant="Primary22Demi109"
              as={'p'}
              mt="30px"
              mb="20px"
              sx={{
                fontSize: '18px',
                fontWeight: 600,
              }}
            >
              {translation.ENABLED_DEVICES}
            </Text>
            {!printers?.data?.some((printer) => printer.enabled) ? (
              <Paragraph
                pt={57}
                pb={57}
                className="text-center"
                variant="Secondary14Medium88"
              >
                {translation.NO_ENABLED_PRINTER_AVAILABLE}
              </Paragraph>
            ) : (
              printers?.data
                ?.filter((printer) => printer.enabled)
                ?.map((printer) => {
                  const uniqueClientId = generateUniqueAlphanumeric(6)
                  return (
                    <MqttEnabledPrinters
                      key={printer?.macAddress}
                      clientId={`${storeData?.data?.id}-${uniqueClientId}`}
                      brokerUrl={process.env.MQTT_BROKER_URL || ''}
                      ForSubscribe={`${translation.STAR_CLOUDPRNT}/${translation.TO_SERVER}/${printer.macAddress}/${translation.CLIENT_STATUS}`}
                      topicForPrinterStatus={`${translation.STAR_CLOUDPRNT}/${translation.TO_DEVICE}/${printer.macAddress}/${translation.REQUEST_CLIENT_STATUS}`}
                      topicForPrintJob={`${translation.STAR_CLOUDPRNT}/${translation.TO_DEVICE}/${printer.macAddress}/${translation.PRINT_JOB}`}
                      printer={printer}
                      setIsDropdownOpen={setIsDropdownOpen}
                      setIsPrinterEdit={(value) => setIsPrinterEdit(value)}
                      setOpenModal={(value) => setOpenModal(value)}
                      setPrinterEditData={(value) => setPrinterEditData(value)}
                      removePrintersHandler={(value) =>
                        removePrintersHandler(value)
                      }
                    />
                  )
                })
            )}
            <Text
              variant="Primary22Demi109"
              mb="20px"
              mt="30px"
              as={'p'}
              sx={{
                fontSize: '18px',
                fontWeight: 600,
              }}
            >
              {translation.DISABLED_DEVICES}
            </Text>
            {!printers?.data?.some((printer) => !printer.enabled) ? (
              <Paragraph
                pt={57}
                pb={57}
                className="text-center"
                variant="Secondary14Medium88"
              >
                {translation.NO_DISABLED_PRINTER_AVAILABLE}
              </Paragraph>
            ) : (
              printers?.data
                ?.filter((printer) => !printer.enabled)
                .map((printer) => {
                  return (
                    <MqttDisabledPrinters
                      key={printer?.macAddress}
                      printer={printer}
                      setIsDropdownOpen={setIsDropdownOpen}
                      setIsPrinterEdit={(value) => setIsPrinterEdit(value)}
                      setOpenModal={(value) => setOpenModal(value)}
                      setPrinterEditData={(value) => setPrinterEditData(value)}
                      removePrintersHandler={(value) =>
                        removePrintersHandler(value)
                      }
                    />
                  )
                })
            )}
          </>
        ) : (
          <Paragraph
            pt={57}
            pb={57}
            className="text-center"
            variant="Secondary14Medium88"
          >
            {translation.NO_ENABLED_PRINTER_AVAILABLE}
          </Paragraph>
        )}
      </div>
      {openModal && (
        <AddPrinterModal
          isOpen={openModal}
          onClose={() => {
            setOpenModal(false)
            setPrinterEditData(null)
          }}
          title={
            isPrinterEdit ? translation.EDIT_PRINTER : translation.ADD_PRINTER
          }
          modalContainer={'add-printer-modal'}
          printerEditData={printerEditData}
          isPrinterEdit={isPrinterEdit}
          setIsPrinterEdit={setIsPrinterEdit}
        />
      )}
      {isDeleteItem && (
        <DeletePrintersModal
          isOpen={isDeleteItem}
          deletePrinter={deletePrinter}
          onClose={() => {
            setIsDeleteItem(false)
          }}
          title={translation.DELETE_PRINTER_CONFIRMATION}
          modalContainer={'delete-printer-modal'}
          isLoading={printers?.loading}
        />
      )}
    </div>
  )
}

export default Printers
