import {
  ACCESS_TOKEN,
  FALSE,
  IS_STORE_DATA_SELECTED,
  IS_TEAM_MEMBER_SELECTED,
  POS_PRODUCT_TYPE,
  SELECTED_CUSTOMER,
  terminalAndOtherSelectionRoutes,
  TRUE,
} from '@/utils/constant'
import {
  getAppRouteBasedOnProductValue,
  hasRouteAccess,
} from '@/utils/functions'
import { routeAccess } from '@/utils/routeAccess'
import { appRoutes } from '@/utils/routes'
import { isRouteExist, isValidProductType } from '@/utils/strings'
import { cookies } from 'next/headers'
import { NextRequest, NextResponse } from 'next/server'

export function middleware(request: NextRequest) {
  const authCookieName =
    process.env.NODE_ENV === 'development'
      ? 'next-auth.session-token'
      : '__Secure-next-auth.session-token'

  const sessionTokenCookie = cookies().get(authCookieName)
  const accessTokenCookie = cookies().get(ACCESS_TOKEN)
  const sessionToken = sessionTokenCookie?.value
  const accessToken = accessTokenCookie?.value

  if (!sessionToken || !accessToken) {
    return NextResponse.redirect(new URL(appRoutes.signIn, request.url))
  }

  const posProductType = cookies().get(POS_PRODUCT_TYPE)
  const isCustomerSelected = cookies().get(SELECTED_CUSTOMER)
  const posProductValue = Number(posProductType?.value)
  const routeRequest = request.nextUrl.pathname
  const checkIsStoreDataSelectedExist = cookies().get(IS_STORE_DATA_SELECTED)
  const checkIsUserSelectedExist = cookies().get(IS_TEAM_MEMBER_SELECTED)

  if (
    checkIsStoreDataSelectedExist &&
    checkIsStoreDataSelectedExist.value === FALSE &&
    routeRequest !== appRoutes.selectStoreData
  ) {
    return NextResponse.redirect(
      new URL(appRoutes.selectStoreData, request.url)
    )
  }

  if (
    request.nextUrl.pathname === appRoutes.home &&
    checkIsStoreDataSelectedExist &&
    checkIsStoreDataSelectedExist.value === TRUE &&
    checkIsUserSelectedExist &&
    checkIsUserSelectedExist.value === FALSE &&
    !terminalAndOtherSelectionRoutes.includes(routeRequest)
  ) {
    return NextResponse.redirect(
      new URL(getAppRouteBasedOnProductValue(posProductValue), request.url)
    )
  }

  if (checkIsStoreDataSelectedExist) {
    if (
      isValidProductType(posProductValue) &&
      routeRequest &&
      isRouteExist(routeRequest)
    ) {
      const productAccess = [...routeAccess[routeRequest]]
      if (!productAccess.includes(posProductValue)) {
        return NextResponse.redirect(
          new URL(getAppRouteBasedOnProductValue(posProductValue), request.url)
        )
      }

      if (
        isCustomerSelected &&
        isCustomerSelected?.value !== TRUE &&
        hasRouteAccess(posProductValue, request.nextUrl.pathname)
      ) {
        const targetUrl = new URL(
          getAppRouteBasedOnProductValue(posProductValue),
          request.url
        ).toString()

        if (request.nextUrl.toString() !== targetUrl) {
          return NextResponse.redirect(targetUrl)
        }
      }
    }
  }
  return NextResponse.next()
}

export const config = {
  matcher: [
    '/poc-main-page',
    '/indexdb',
    '/redux',
    '/sse',
    '/websocket',
    '/theme',
    '/default-state',
    '/accounting',
    '/delivery',
    '/dashboard',
    '/profile',
    '/tickets',
    '/settings/hardware',
    '/settings/my-team',
    '/settings/operations',
    '/settings/reports',
    '/settings/store-details',
    '/item-library',
    '/settings/item-library/categories',
    '/settings/item-library/items-price-list',
    '/settings/item-library/variants',
    '/settings/hardware/printer',
    '/settings/hardware/printer-discovery',
    '/settings/hardware/payment-terminals',
    '/settings/reports/revenue-summary',
    '/settings/reports/sales-analysis',
    '/settings/reports/item-transactions',
    '/pos',
    '/arBilling',
    '/delivery',
    '/assign-rack',
    '/select-store-data',
    '/select-printer-data',
    '/select-payment-terminal-data',
    '/select-user',
  ],
}
