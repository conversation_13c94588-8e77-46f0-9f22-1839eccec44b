import { AssignRackData } from '@/types/module/assignRackModule'
import { CommonIdbResponse, IdbResponse } from '@/types/module/commonModule'
import { TicketValidationResult } from '@/types/module/customerTicketsModule'

export interface AssignRackDbOperations {
  getAllAssignRacks(): Promise<AssignRackData[]>
  addAssignRack(rack: AssignRackData): Promise<IdbResponse<AssignRackData>>
  addMultipleAssignRacks(rack: AssignRackData[]): Promise<CommonIdbResponse>
  deleteAllAssignRacks(): Promise<CommonIdbResponse>
  validateTicketAndRackData(
    ticketId: number,
    rackId: string,
    multipleRackAssignment: boolean
  ): Promise<TicketValidationResult>
}
