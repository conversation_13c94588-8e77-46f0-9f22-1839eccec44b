import {
  AssignRackData,
  AssignRackPayload,
  GetAssignRackDataProps,
} from '@/types/module/assignRackModule'
import { TicketValidationResult } from '@/types/module/customerTicketsModule'
import { StringType } from '@/types/store/actions/common.actions'
import { AxiosError } from 'axios'

export interface AssignRackDataFailure {
  type: string
  error: AxiosError | Error
}

export interface GetAssignRackData {
  type: string
  payload: GetAssignRackDataProps
  callBack: (res: boolean) => void
}

export interface GetAssignRackSuccess {
  type: string
  payload: AssignRackData[]
}

export interface AssignRackDataAction {
  type: string
  payload: AssignRackPayload
  callBack: (res: boolean, error?: string) => void
}

export interface CheckDataValidation {
  type: string
  ticketId: number
  rackId: string
  callBack: (result: TicketValidationResult) => void
}

export interface AssignRackDataSuccess {
  type: string
  payload: AssignRackData[]
}

export type assignRackDataType =
  | StringType
  | AssignRackDataFailure
  | GetAssignRackSuccess
  | AssignRackDataAction
  | AssignRackDataSuccess
