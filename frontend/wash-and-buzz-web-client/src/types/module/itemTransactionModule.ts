import { ColumnDef, OnChangeFn, SortingState } from '@tanstack/react-table'

export type ItemTransactions = {
  itemId: string
  name: string
  price: number | null
  quantity: number
  surcharge: number
  discount: number
  total: number
}

export interface ItemTransactionsTableProps<T> {
  columns: ColumnDef<T>[]
  data: T[]
  sorting?: SortingState
  setSorting?: OnChangeFn<SortingState>
  onRowClick?: (row: T) => void
  selectedRow?: (row: T) => boolean
  isLoading?: boolean
  loader?: React.ReactNode
  className?: string
  onTdClick?: (row: number) => void
}

export interface ColumnHeader {
  id: number
  header: string
}

export enum ItemTransactionsTableColumn {
  ITEM = 'Item',
  BASE_PRICE = 'Base Price',
  QTY = 'Qty',
  SURCHARGE = 'Surcharge',
  DISCOUNT = 'Discount',
  TOTAL = 'Total',
}
