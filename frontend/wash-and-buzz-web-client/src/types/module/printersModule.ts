import { TicketData } from '@/types/module/ticketNdSalesModule'

export interface MqttMessage {
  title: string
  status: string
  printerMAC: string
  statusCode: string
  printingInProgress: boolean
  clientAction: string | null
  display: {
    name: string
    status: {
      connected: boolean
    }
  }[]
  barcodeReader: {
    name: string
    status: {
      connected: boolean
      claimed: boolean
    }
  }[]
}

export type PrintJob = {
  title: string
  jobToken: string
  jobType: string
  mediaTypes: string[]
  printData: string
}

export type PrinterStatus = {
  title: string
}

export interface PrinterData {
  id?: number
  printerId?: string
  enabled?: boolean
  orderIndex?: number
  maskedField?: string
  storeId?: number
  name: string
  macAddress: string
  username: string
  usage: PrinterUsage[]
  isSelected?: boolean
}

export interface PrinterDataProps {
  printer: PrinterData
  isLoading?: boolean
  brokerUrl: string
  ForSubscribe: string
  topicForPrinterStatus: string
  topicForPrintJob: string
  setIsDropdownOpen: (value: boolean) => void
  clientId: string
  setIsPrinterEdit: (value: boolean) => void
  setOpenModal: (value: boolean) => void
  setPrinterEditData: (value: PrinterData) => void
  removePrintersHandler: (value: PrinterData) => void
}
export interface PrinterDisabledDataProps {
  printer: PrinterData
  isLoading?: boolean
  setIsDropdownOpen: (value: boolean) => void
  setIsPrinterEdit: (value: boolean) => void
  setOpenModal: (value: boolean) => void
  setPrinterEditData: (value: PrinterData) => void
  removePrintersHandler: (value: PrinterData) => void
}

export interface PrinterDataPartially {
  id?: number
  printerId?: string
  enabled?: boolean
  orderIndex?: number
  maskedField?: string
  storeId?: number
  name?: string
  macAddress?: string
  username?: string
  usage?: PrinterUsage[]
}

export interface PrinterUsage {
  name: string
  enabled: boolean
}

export interface PrintersBackendData {
  id?: number
  printerId: string
  name: string
  macAddress: string
  username: string
  usage: PrinterUsage[] | []
  enabled: boolean
}

export interface AddPrinterModalProps {
  modal?: React.CSSProperties
  isOpen: boolean
  onClose: () => void
  closeIcon?: string
  modalContainer: string
  title: string
  showCloseIcon?: boolean
  isEditMode?: boolean
  printerEditData?: PrinterData | null
  onHandleSubmit?: (updatedFields: UpdatePrinterIdbData) => void
  isPrinterEdit: boolean
  setIsPrinterEdit: (value: boolean) => void
}

export interface PrintOptionsModalProps {
  modal?: React.CSSProperties
  isOpen: boolean
  onClose: () => void
  closeIcon?: string
  modalContainer: string
  title: string
  showCloseIcon?: boolean
  ticketData?: TicketData
  printers?: PrinterData[] | null
  options: { label: string; onClick: () => void }[]
}

export enum PrintOptions {
  PRINT_STORE_COPY = 'Print Store Copy',
  PRINT_SUB_TICKET = 'Print Sub-Ticket',
  PRINT_ALL = 'Print All',
  PRINT_PAYMENT_RECEIPT = 'Print Payment Receipt',
  ALL = 'All',
  QUICK_DROP_OFF_COPY = 'Quick Drop-Off Copy',
}

export interface AddPrinterData {
  storeId: number
  name: string
  macAddress: string
  username: string
  password?: string
  usage: PrinterUsage[] | []
}

export interface StorePrinterData {
  storeId: number
  printerId: string
}

export interface DropdownItems {
  label: string
  onClick: (data: PrinterData) => void
}
export interface DropdownItemsProps {
  dropdownItems: DropdownItems[]
  setIsDropdownOpen: (value: boolean) => void
  printer: PrinterData
}

// Printer Discovery Types
export enum PrinterConnectionMethod {
  USB = 'USB',
  BLUETOOTH = 'BLUETOOTH',
  ETHERNET = 'ETHERNET',
  NETWORK = 'NETWORK',
  MANUAL = 'MANUAL',
}

export enum PrinterDiscoveryStatus {
  IDLE = 'IDLE',
  SCANNING = 'SCANNING',
  FOUND = 'FOUND',
  ERROR = 'ERROR',
  CONNECTING = 'CONNECTING',
  CONNECTED = 'CONNECTED',
  TESTING = 'TESTING',
  TEST_SUCCESS = 'TEST_SUCCESS',
  TEST_FAILED = 'TEST_FAILED',
}

export interface DiscoveredPrinter {
  id: string
  name: string
  model?: string
  manufacturer?: string
  connectionMethod: PrinterConnectionMethod
  connectionDetails: {
    ipAddress?: string
    macAddress?: string
    port?: number
    deviceId?: string
    bluetoothId?: string
    usbVendorId?: number
    usbProductId?: number
  }
  capabilities?: {
    supportsEpson?: boolean
    supportedLanguages?: string[]
    paperSizes?: string[]
    resolution?: string
  }
  status: PrinterDiscoveryStatus
  signal?: number // For Bluetooth/WiFi signal strength
  lastSeen?: Date
  isOnline?: boolean
}

export interface PrinterDiscoveryOptions {
  connectionMethods: PrinterConnectionMethod[]
  timeout?: number
  networkRange?: string
  includeOffline?: boolean
  epsonOnly?: boolean
}

export interface PrinterDiscoveryResult {
  success: boolean
  printers: DiscoveredPrinter[]
  errors?: string[]
  scanDuration?: number
}

export interface PrinterTestResult {
  success: boolean
  printerId: string
  message?: string
  error?: string
  timestamp: Date
}

export interface EpsonTestPrintData {
  storeName?: string
  storeAddress?: string
  testPattern: boolean
  includeBarcode: boolean
  includeQRCode: boolean
  customText?: string
}

export interface DeletePrinterProps {
  isOpen: boolean
  onClose: () => void
  closeIcon?: string
  modalContainer?: string
  deletePrinter?: PrinterData
  title: string
  isLoading: boolean
  showCloseIcon?: boolean
  modal?: React.CSSProperties
  onClick?: () => void
}

export interface UpdatePrinterData {
  storeId: number
  printerId: string
  name?: string
  username?: string
  password?: string
  usage?: PrinterUsage[]
  enabled?: boolean
}
export interface UpdatePrinterIdbData {
  printerId?: string
  enabled?: boolean
  orderIndex?: number
  maskedField?: string
  storeId?: number
  name?: string
  macAddress?: string
  username?: string
  usage?: PrinterUsage[]
}

export interface SelectedPrinter {
  name: string
  id: number
  macAddress: string
}

export enum PrintingStatus {
  PRINTING_INPROGRESS = 'PRINTING_INPROGRESS',
  PRINTING_COMPLETE = 'PRINTING_COMPLETE',
}

export enum EnabledDeviceState {
  EDIT = 'Edit',
  REFRESH = 'Refresh',
  DISABLE = 'Disable',
  DELETE = 'Delete',
  CONNECT = 'Connect',
  DISCONNECT = 'Disconnect',
}

export enum DeviceStatus {
  Pending = 'Pending',
  Connected = 'Connected',
  NotConnected = 'Not Connected',
  DeviceNotFound = 'Device Not Found',
  Disabled = 'Disabled',
}
