/**
 * Printer Discovery Service
 * Comprehensive service for discovering Epson printers via multiple connection methods
 */

import {
  PrinterConnectionMethod,
  PrinterDiscoveryOptions,
  PrinterDiscoveryResult,
  PrinterDiscoveryStatus,
  DiscoveredPrinter,
  PrinterTestResult,
  EpsonTestPrintData,
} from '@/types/module/printersModule'
import PrinterDiscoveryErrorHandler, {
  PrinterDiscoveryError,
  PrinterDiscoveryErrorType,
} from '@/utils/printerDiscoveryErrors'

export class PrinterDiscoveryService {
  private static instance: PrinterDiscoveryService
  private discoveredPrinters: Map<string, DiscoveredPrinter> = new Map()
  private isScanning = false
  private scanAbortController: AbortController | null = null

  private constructor() {}

  public static getInstance(): PrinterDiscoveryService {
    if (!PrinterDiscoveryService.instance) {
      PrinterDiscoveryService.instance = new PrinterDiscoveryService()
    }
    return PrinterDiscoveryService.instance
  }

  /**
   * Main discovery method that orchestrates all discovery methods
   */
  public async discoverPrinters(
    options: PrinterDiscoveryOptions
  ): Promise<PrinterDiscoveryResult> {
    if (this.isScanning) {
      throw new Error('Discovery already in progress')
    }

    this.isScanning = true
    this.scanAbortController = new AbortController()
    const startTime = Date.now()
    const allPrinters: DiscoveredPrinter[] = []
    const errors: string[] = []

    try {
      // Run discovery methods in parallel
      const discoveryPromises = options.connectionMethods.map(
        async (method) => {
          try {
            switch (method) {
              case PrinterConnectionMethod.NETWORK:
                return await this.discoverNetworkPrinters(options)
              case PrinterConnectionMethod.USB:
                return await this.discoverUSBPrinters(options)
              case PrinterConnectionMethod.BLUETOOTH:
                return await this.discoverBluetoothPrinters(options)
              case PrinterConnectionMethod.ETHERNET:
                return await this.discoverEthernetPrinters(options)
              default:
                return []
            }
          } catch (error) {
            const discoveryError = PrinterDiscoveryErrorHandler.createError(
              error,
              method
            )
            errors.push(`${method}: ${discoveryError.userMessage}`)
            console.error(`Discovery error for ${method}:`, discoveryError)
            return []
          }
        }
      )

      const results = await Promise.allSettled(discoveryPromises)

      results.forEach((result, index) => {
        if (result.status === 'fulfilled') {
          allPrinters.push(...result.value)
        } else {
          errors.push(`${options.connectionMethods[index]}: ${result.reason}`)
        }
      })

      // Remove duplicates based on MAC address or device ID
      const uniquePrinters = this.removeDuplicatePrinters(allPrinters)

      // Update internal cache
      uniquePrinters.forEach((printer) => {
        this.discoveredPrinters.set(printer.id, printer)
      })

      return {
        success: true,
        printers: uniquePrinters,
        errors: errors.length > 0 ? errors : undefined,
        scanDuration: Date.now() - startTime,
      }
    } catch (error) {
      return {
        success: false,
        printers: [],
        errors: [error instanceof Error ? error.message : 'Unknown error'],
        scanDuration: Date.now() - startTime,
      }
    } finally {
      this.isScanning = false
      this.scanAbortController = null
    }
  }

  /**
   * Discover printers on the network
   */
  private async discoverNetworkPrinters(
    options: PrinterDiscoveryOptions
  ): Promise<DiscoveredPrinter[]> {
    const printers: DiscoveredPrinter[] = []

    // Get local network range
    const networkRange =
      options.networkRange || (await this.getLocalNetworkRange())

    // Scan common printer ports
    const commonPorts = [9100, 515, 631, 80, 443]
    const ipRanges = this.generateIPRange(networkRange)

    const scanPromises = ipRanges.slice(0, 254).map(async (ip) => {
      for (const port of commonPorts) {
        try {
          const printer = await this.scanNetworkPrinter(
            ip,
            port,
            options.timeout || 3000
          )
          if (printer) {
            printers.push(printer)
            break // Found printer on this IP, no need to check other ports
          }
        } catch (error) {
          // Continue scanning other IPs/ports
        }
      }
    })

    await Promise.allSettled(scanPromises)
    return printers
  }

  /**
   * Discover USB printers using WebUSB API
   */
  private async discoverUSBPrinters(
    options: PrinterDiscoveryOptions
  ): Promise<DiscoveredPrinter[]> {
    if (typeof navigator === 'undefined' || !navigator.usb) {
      throw new Error('WebUSB not supported in this browser')
    }

    try {
      // First, get already paired devices
      const pairedDevices = await navigator.usb.getDevices()
      const printers: DiscoveredPrinter[] = []

      // Add already paired devices
      for (const device of pairedDevices) {
        if (this.isEpsonUSBDevice(device) || !options.epsonOnly) {
          const printer = await this.createUSBPrinterInfo(device)
          printers.push(printer)
        }
      }

      // If no devices found, try to request device access
      if (printers.length === 0) {
        try {
          const newDevice = await this.requestUSBDevice(options.epsonOnly)
          if (newDevice) {
            const printer = await this.createUSBPrinterInfo(newDevice)
            printers.push(printer)
          }
        } catch (requestError) {
          // User cancelled or no device selected, continue with empty list
          console.log('USB device request cancelled or failed:', requestError)
        }
      }

      return printers
    } catch (error) {
      throw new Error(
        `USB discovery failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      )
    }
  }

  /**
   * Request access to a USB device
   */
  private async requestUSBDevice(
    epsonOnly: boolean = true
  ): Promise<USBDevice | null> {
    if (typeof navigator === 'undefined' || !navigator.usb) {
      throw new Error('WebUSB not supported')
    }

    try {
      const filters = []

      if (epsonOnly) {
        // Epson vendor ID
        filters.push({ vendorId: 0x04b8 })
      } else {
        // Common printer vendor IDs
        filters.push(
          { vendorId: 0x04b8 }, // Epson
          { vendorId: 0x08e4 }, // Star Micronics
          { vendorId: 0x0a5f }, // Zebra
          { vendorId: 0x04f9 }, // Brother
          { vendorId: 0x03f0 }, // HP
          { vendorId: 0x04a9 } // Canon
        )
      }

      const device = await navigator.usb.requestDevice({
        filters: filters,
      })

      return device
    } catch (error) {
      if (error instanceof Error && error.name === 'NotFoundError') {
        // User cancelled device selection
        return null
      }
      throw error
    }
  }

  /**
   * Discover Bluetooth printers using Web Bluetooth API
   */
  private async discoverBluetoothPrinters(
    options: PrinterDiscoveryOptions
  ): Promise<DiscoveredPrinter[]> {
    if (typeof navigator === 'undefined' || !navigator.bluetooth) {
      throw new Error('Web Bluetooth not supported in this browser')
    }

    try {
      const printers: DiscoveredPrinter[] = []

      // First, try to get already paired devices
      try {
        const pairedDevices = await navigator.bluetooth.getDevices()
        for (const device of pairedDevices) {
          if (this.isBluetoothPrinter(device, options.epsonOnly)) {
            const printer = await this.createBluetoothPrinterInfo(device)
            printers.push(printer)
          }
        }
      } catch (error) {
        // getDevices might not be supported, continue with requestDevice
        console.log('Bluetooth getDevices not supported:', error)
      }

      // If no paired devices found, request new device
      if (printers.length === 0) {
        try {
          const device = await this.requestBluetoothDevice(options.epsonOnly)
          if (device) {
            const printer = await this.createBluetoothPrinterInfo(device)
            printers.push(printer)
          }
        } catch (requestError) {
          // User cancelled or no device selected
          console.log(
            'Bluetooth device request cancelled or failed:',
            requestError
          )
        }
      }

      return printers
    } catch (error) {
      throw new Error(
        `Bluetooth discovery failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      )
    }
  }

  /**
   * Request access to a Bluetooth device
   */
  private async requestBluetoothDevice(
    epsonOnly: boolean = true
  ): Promise<BluetoothDevice | null> {
    if (typeof navigator === 'undefined' || !navigator.bluetooth) {
      throw new Error('Web Bluetooth not supported')
    }

    try {
      const filters = []
      const optionalServices = ['battery_service', 'device_information']

      if (epsonOnly) {
        filters.push(
          { namePrefix: 'EPSON' },
          { namePrefix: 'TM-' },
          { namePrefix: 'Epson' }
        )
      } else {
        filters.push(
          { namePrefix: 'EPSON' },
          { namePrefix: 'TM-' },
          { namePrefix: 'Epson' },
          { namePrefix: 'Star' },
          { namePrefix: 'TSP' },
          { namePrefix: 'Zebra' },
          { namePrefix: 'Brother' },
          { namePrefix: 'Canon' }
        )
      }

      // Add service UUIDs for printer services
      filters.push(
        { services: ['000018f0-0000-1000-8000-00805f9b34fb'] }, // Serial Port Profile
        { services: ['0000110e-0000-1000-8000-00805f9b34fb'] } // A/V Remote Control Profile
      )

      const device = await navigator.bluetooth.requestDevice({
        filters: filters,
        optionalServices: optionalServices,
      })

      return device
    } catch (error) {
      if (error instanceof Error && error.name === 'NotFoundError') {
        // User cancelled device selection
        return null
      }
      throw error
    }
  }

  /**
   * Check if a Bluetooth device is a printer
   */
  private isBluetoothPrinter(
    device: BluetoothDevice,
    epsonOnly: boolean = true
  ): boolean {
    const name = device.name?.toLowerCase() || ''

    if (epsonOnly) {
      return name.includes('epson') || name.includes('tm-')
    }

    const printerKeywords = [
      'epson',
      'tm-',
      'star',
      'tsp',
      'zebra',
      'brother',
      'canon',
      'printer',
    ]
    return printerKeywords.some((keyword) => name.includes(keyword))
  }

  /**
   * Discover Ethernet printers (similar to network but with specific Ethernet protocols)
   */
  private async discoverEthernetPrinters(
    options: PrinterDiscoveryOptions
  ): Promise<DiscoveredPrinter[]> {
    // For now, delegate to network discovery
    // In a full implementation, this could use specific Ethernet protocols
    return this.discoverNetworkPrinters(options)
  }

  /**
   * Test print functionality
   */
  public async testPrint(
    printer: DiscoveredPrinter,
    testData: EpsonTestPrintData
  ): Promise<PrinterTestResult> {
    try {
      // Update printer status
      printer.status = PrinterDiscoveryStatus.TESTING
      this.discoveredPrinters.set(printer.id, printer)

      // Generate Epson test print data
      const printData = await this.generateEpsonTestPrint(testData)

      // Send print job based on connection method
      const success = await this.sendTestPrintJob(printer, printData)

      // Update printer status
      printer.status = success
        ? PrinterDiscoveryStatus.TEST_SUCCESS
        : PrinterDiscoveryStatus.TEST_FAILED
      this.discoveredPrinters.set(printer.id, printer)

      return {
        success,
        printerId: printer.id,
        message: success ? 'Test print successful' : 'Test print failed',
        timestamp: new Date(),
      }
    } catch (error) {
      printer.status = PrinterDiscoveryStatus.TEST_FAILED
      this.discoveredPrinters.set(printer.id, printer)

      return {
        success: false,
        printerId: printer.id,
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date(),
      }
    }
  }

  /**
   * Stop current discovery process
   */
  public stopDiscovery(): void {
    if (this.scanAbortController) {
      this.scanAbortController.abort()
    }
    this.isScanning = false
  }

  /**
   * Get cached discovered printers
   */
  public getDiscoveredPrinters(): DiscoveredPrinter[] {
    return Array.from(this.discoveredPrinters.values())
  }

  /**
   * Clear discovered printers cache
   */
  public clearCache(): void {
    this.discoveredPrinters.clear()
  }

  // Helper methods will be implemented in the next part
  private removeDuplicatePrinters(
    printers: DiscoveredPrinter[]
  ): DiscoveredPrinter[] {
    const seen = new Set<string>()
    return printers.filter((printer) => {
      const key =
        printer.connectionDetails.macAddress ||
        printer.connectionDetails.deviceId ||
        printer.id
      if (seen.has(key)) {
        return false
      }
      seen.add(key)
      return true
    })
  }

  private async getLocalNetworkRange(): Promise<string> {
    // Default to common private network ranges
    return '***********/24'
  }

  private generateIPRange(networkRange: string): string[] {
    // Simple implementation for /24 networks
    const [baseIP] = networkRange.split('/')
    const [a, b, c] = baseIP.split('.').map(Number)
    const ips: string[] = []

    for (let i = 1; i < 255; i++) {
      ips.push(`${a}.${b}.${c}.${i}`)
    }

    return ips
  }

  private async scanNetworkPrinter(
    ip: string,
    port: number,
    timeout: number
  ): Promise<DiscoveredPrinter | null> {
    try {
      // Use fetch with timeout to check if printer responds
      const controller = new AbortController()
      const timeoutId = setTimeout(() => controller.abort(), timeout)

      try {
        // Try to connect to common printer endpoints
        const endpoints = [
          `http://${ip}:${port}/`,
          `http://${ip}/`,
          `http://${ip}:631/`,
          `http://${ip}:80/`,
        ]

        for (const endpoint of endpoints) {
          try {
            const response = await fetch(endpoint, {
              method: 'HEAD',
              signal: controller.signal,
              mode: 'no-cors', // Allow cross-origin requests
            })

            // If we get any response, there's likely a device here
            const printer = await this.identifyNetworkPrinter(
              ip,
              port,
              endpoint
            )
            if (printer) {
              clearTimeout(timeoutId)
              return printer
            }
          } catch (fetchError) {
            // Continue to next endpoint
            continue
          }
        }

        // Try WebSocket connection for raw printer communication
        const wsResult = await this.tryWebSocketConnection(ip, port, timeout)
        if (wsResult) {
          clearTimeout(timeoutId)
          return wsResult
        }
      } finally {
        clearTimeout(timeoutId)
      }

      return null
    } catch (error) {
      return null
    }
  }

  private async identifyNetworkPrinter(
    ip: string,
    port: number,
    endpoint: string
  ): Promise<DiscoveredPrinter | null> {
    try {
      // Try to get printer information via HTTP
      const response = await fetch(endpoint, {
        method: 'GET',
        mode: 'no-cors',
      })

      // Create a basic network printer entry
      const printer: DiscoveredPrinter = {
        id: `network-${ip}-${port}`,
        name: `Network Printer (${ip})`,
        manufacturer: 'Unknown',
        connectionMethod: PrinterConnectionMethod.NETWORK,
        connectionDetails: {
          ipAddress: ip,
          port: port,
        },
        capabilities: {
          supportsEpson: true, // Assume Epson support for now
          supportedLanguages: ['ESC/POS'],
        },
        status: PrinterDiscoveryStatus.FOUND,
        lastSeen: new Date(),
        isOnline: true,
      }

      // Try to get more specific information
      await this.enrichPrinterInfo(printer, endpoint)

      return printer
    } catch (error) {
      return null
    }
  }

  private async enrichPrinterInfo(
    printer: DiscoveredPrinter,
    endpoint: string
  ): Promise<void> {
    try {
      // Try common printer info endpoints
      const infoEndpoints = [
        `${endpoint}status`,
        `${endpoint}info`,
        `${endpoint}cgi-bin/status`,
        `${endpoint}PRESENTATION/ADVANCED/INFO_PRTINFO/TOP`,
      ]

      for (const infoEndpoint of infoEndpoints) {
        try {
          const response = await fetch(infoEndpoint, {
            method: 'GET',
            mode: 'no-cors',
          })

          // Since we can't read the response due to CORS,
          // we'll make educated guesses based on common patterns
          if (printer.connectionDetails.ipAddress) {
            // Try to determine manufacturer from IP patterns or common ports
            if (printer.connectionDetails.port === 9100) {
              printer.capabilities = {
                ...printer.capabilities,
                supportsEpson: true,
                supportedLanguages: ['ESC/POS', 'Raw'],
              }
            }
          }
          break
        } catch (error) {
          continue
        }
      }
    } catch (error) {
      // Enrichment failed, keep basic info
    }
  }

  private async tryWebSocketConnection(
    ip: string,
    port: number,
    timeout: number
  ): Promise<DiscoveredPrinter | null> {
    return new Promise((resolve) => {
      try {
        const ws = new WebSocket(`ws://${ip}:${port}`)
        const timeoutId = setTimeout(() => {
          ws.close()
          resolve(null)
        }, timeout)

        ws.onopen = () => {
          clearTimeout(timeoutId)
          ws.close()

          const printer: DiscoveredPrinter = {
            id: `websocket-${ip}-${port}`,
            name: `WebSocket Printer (${ip})`,
            manufacturer: 'Unknown',
            connectionMethod: PrinterConnectionMethod.NETWORK,
            connectionDetails: {
              ipAddress: ip,
              port: port,
            },
            capabilities: {
              supportsEpson: true,
              supportedLanguages: ['Raw'],
            },
            status: PrinterDiscoveryStatus.FOUND,
            lastSeen: new Date(),
            isOnline: true,
          }

          resolve(printer)
        }

        ws.onerror = () => {
          clearTimeout(timeoutId)
          resolve(null)
        }

        ws.onclose = () => {
          clearTimeout(timeoutId)
          resolve(null)
        }
      } catch (error) {
        resolve(null)
      }
    })
  }

  private isEpsonUSBDevice(device: USBDevice): boolean {
    // Epson vendor ID is 0x04b8
    return device.vendorId === 0x04b8
  }

  private async createUSBPrinterInfo(
    device: USBDevice
  ): Promise<DiscoveredPrinter> {
    let deviceName = device.productName || `USB Device ${device.productId}`
    let manufacturer = device.manufacturerName || 'Unknown'
    let capabilities = {
      supportsEpson: false,
      supportedLanguages: ['Raw'] as string[],
    }

    // Enhanced device identification
    if (this.isEpsonUSBDevice(device)) {
      manufacturer = 'Epson'
      capabilities.supportsEpson = true
      capabilities.supportedLanguages = ['ESC/POS', 'Raw']

      // Try to get more specific model info
      const modelInfo = this.getEpsonModelInfo(device.productId)
      if (modelInfo) {
        deviceName = modelInfo.name
        capabilities = { ...capabilities, ...modelInfo.capabilities }
      }
    } else {
      // Check other manufacturers
      const vendorInfo = this.getVendorInfo(device.vendorId)
      if (vendorInfo) {
        manufacturer = vendorInfo.name
        capabilities = { ...capabilities, ...vendorInfo.capabilities }
      }
    }

    // Try to get device status
    let isOnline = true
    try {
      if (!device.opened) {
        await device.open()
        await device.close()
      }
    } catch (error) {
      isOnline = false
    }

    return {
      id: `usb-${device.vendorId}-${device.productId}-${device.serialNumber || 'unknown'}`,
      name: deviceName,
      manufacturer: manufacturer,
      connectionMethod: PrinterConnectionMethod.USB,
      connectionDetails: {
        deviceId: device.serialNumber,
        usbVendorId: device.vendorId,
        usbProductId: device.productId,
      },
      capabilities: capabilities,
      status: PrinterDiscoveryStatus.FOUND,
      lastSeen: new Date(),
      isOnline: isOnline,
    }
  }

  /**
   * Get Epson model information based on product ID
   */
  private getEpsonModelInfo(
    productId: number
  ): { name: string; capabilities: any } | null {
    const epsonModels: { [key: number]: { name: string; capabilities: any } } =
      {
        0x0202: {
          name: 'TM-T88VI',
          capabilities: { paperSizes: ['80mm'], resolution: '203dpi' },
        },
        0x0220: {
          name: 'TM-T20III',
          capabilities: { paperSizes: ['80mm'], resolution: '203dpi' },
        },
        0x0221: {
          name: 'TM-T82III',
          capabilities: { paperSizes: ['80mm'], resolution: '203dpi' },
        },
        0x0222: {
          name: 'TM-T83III',
          capabilities: { paperSizes: ['80mm'], resolution: '203dpi' },
        },
        0x0223: {
          name: 'TM-T88VI-iHUB',
          capabilities: { paperSizes: ['80mm'], resolution: '203dpi' },
        },
        0x0224: {
          name: 'TM-m30',
          capabilities: { paperSizes: ['80mm'], resolution: '203dpi' },
        },
        0x0225: {
          name: 'TM-m30II',
          capabilities: { paperSizes: ['80mm'], resolution: '203dpi' },
        },
      }

    return epsonModels[productId] || null
  }

  /**
   * Get vendor information based on vendor ID
   */
  private getVendorInfo(
    vendorId: number
  ): { name: string; capabilities: any } | null {
    const vendors: { [key: number]: { name: string; capabilities: any } } = {
      0x04b8: {
        name: 'Epson',
        capabilities: { supportsEpson: true, supportedLanguages: ['ESC/POS'] },
      },
      0x08e4: {
        name: 'Star Micronics',
        capabilities: { supportedLanguages: ['Star Line Mode'] },
      },
      0x0a5f: {
        name: 'Zebra',
        capabilities: { supportedLanguages: ['ZPL', 'EPL'] },
      },
      0x04f9: {
        name: 'Brother',
        capabilities: { supportedLanguages: ['Brother P-touch'] },
      },
      0x03f0: {
        name: 'HP',
        capabilities: { supportedLanguages: ['PCL', 'PostScript'] },
      },
      0x04a9: {
        name: 'Canon',
        capabilities: { supportedLanguages: ['Canon BJNP'] },
      },
    }

    return vendors[vendorId] || null
  }

  private async createBluetoothPrinterInfo(
    device: BluetoothDevice
  ): Promise<DiscoveredPrinter> {
    let deviceName = device.name || 'Bluetooth Printer'
    let manufacturer = 'Unknown'
    let capabilities = {
      supportsEpson: false,
      supportedLanguages: ['Raw'] as string[],
    }
    let signal = 0

    // Enhanced device identification
    const name = device.name?.toLowerCase() || ''
    if (name.includes('epson') || name.includes('tm-')) {
      manufacturer = 'Epson'
      capabilities.supportsEpson = true
      capabilities.supportedLanguages = ['ESC/POS', 'Raw']

      // Try to extract model from name
      if (name.includes('tm-')) {
        const modelMatch = name.match(/tm-[a-z0-9]+/i)
        if (modelMatch) {
          deviceName = modelMatch[0].toUpperCase()
        }
      }
    } else if (name.includes('star') || name.includes('tsp')) {
      manufacturer = 'Star Micronics'
      capabilities.supportedLanguages = ['Star Line Mode', 'Raw']
    } else if (name.includes('zebra')) {
      manufacturer = 'Zebra'
      capabilities.supportedLanguages = ['ZPL', 'EPL', 'Raw']
    }

    // Try to get device info and signal strength
    let isOnline = true
    try {
      if (device.gatt) {
        // Try to connect to get more info
        const server = await device.gatt.connect()

        // Try to get signal strength (RSSI)
        if ('readRSSI' in device.gatt) {
          try {
            const rssi = await (device.gatt as any).readRSSI()
            // Convert RSSI to percentage (rough approximation)
            signal = Math.max(0, Math.min(100, (rssi + 100) * 2))
          } catch (error) {
            // RSSI not available
          }
        }

        // Try to get device information service
        try {
          const service = await server.getPrimaryService('device_information')
          const characteristics = await service.getCharacteristics()

          for (const char of characteristics) {
            try {
              const value = await char.readValue()
              const text = new TextDecoder().decode(value)

              // Update device info based on characteristic
              if (char.uuid.includes('2a29')) {
                // Manufacturer Name
                manufacturer = text
              } else if (char.uuid.includes('2a24')) {
                // Model Number
                deviceName = text
              }
            } catch (error) {
              // Characteristic not readable
            }
          }
        } catch (error) {
          // Device information service not available
        }

        await server.disconnect()
      }
    } catch (error) {
      isOnline = false
      console.log('Failed to connect to Bluetooth device for info:', error)
    }

    return {
      id: `bluetooth-${device.id}`,
      name: deviceName,
      manufacturer: manufacturer,
      connectionMethod: PrinterConnectionMethod.BLUETOOTH,
      connectionDetails: {
        bluetoothId: device.id,
        deviceId: device.id,
      },
      capabilities: capabilities,
      status: PrinterDiscoveryStatus.FOUND,
      signal: signal > 0 ? signal : undefined,
      lastSeen: new Date(),
      isOnline: isOnline,
    }
  }

  private async generateEpsonTestPrint(
    testData: EpsonTestPrintData
  ): Promise<string> {
    // Import the test print generator
    const { generateEpsonTestPrint, encodeTestPrintData } = await import(
      '@/utils/epsonTestPrint'
    )

    // Generate the test print content
    const testPrintContent = generateEpsonTestPrint(testData)

    // Encode for transmission
    return encodeTestPrintData(testPrintContent)
  }

  private async sendTestPrintJob(
    printer: DiscoveredPrinter,
    printData: string
  ): Promise<boolean> {
    try {
      switch (printer.connectionMethod) {
        case PrinterConnectionMethod.NETWORK:
        case PrinterConnectionMethod.ETHERNET:
          return await this.sendNetworkTestPrint(printer, printData)

        case PrinterConnectionMethod.USB:
          return await this.sendUSBTestPrint(printer, printData)

        case PrinterConnectionMethod.BLUETOOTH:
          return await this.sendBluetoothTestPrint(printer, printData)

        case PrinterConnectionMethod.MANUAL:
          // For manually added printers, try network first
          return await this.sendNetworkTestPrint(printer, printData)

        default:
          throw new Error(
            `Unsupported connection method: ${printer.connectionMethod}`
          )
      }
    } catch (error) {
      console.error('Test print failed:', error)
      return false
    }
  }

  private async sendNetworkTestPrint(
    printer: DiscoveredPrinter,
    printData: string
  ): Promise<boolean> {
    try {
      // Try to send via existing MQTT infrastructure if available
      if (typeof window !== 'undefined' && (window as any).mqttClient) {
        return await this.sendMQTTTestPrint(printer, printData)
      }

      // Fallback to direct network printing
      return await this.sendDirectNetworkPrint(printer, printData)
    } catch (error) {
      console.error('Network test print failed:', error)
      return false
    }
  }

  private async sendMQTTTestPrint(
    printer: DiscoveredPrinter,
    printData: string
  ): Promise<boolean> {
    try {
      // Try enhanced MQTT service first
      try {
        const mqttService = await import('./printerDiscoveryMQTT.service')
        const service = mqttService.default

        if (service && typeof service.sendLegacyPrintJob === 'function') {
          const macAddress = printer.connectionDetails.macAddress
          if (macAddress) {
            return await service.sendLegacyPrintJob(macAddress, printData)
          }
        }
      } catch (mqttServiceError) {
        console.log('Enhanced MQTT service not available, using legacy method')
      }

      // Fallback to existing MQTT infrastructure
      const { publishToTopicWithPromise } = await import('@/utils/useMqtt')
      const { generateUniqueAlphanumeric } = await import('@/utils/strings')
      const { translation } = await import('@/utils/translation')
      const { JOB_TYPE, MEDIA_TYPE } = await import('@/utils/constant')

      const jobToken = `TEST-${generateUniqueAlphanumeric(7)}`
      const macAddress = printer.connectionDetails.macAddress || 'UNKNOWN'

      const topic = `${translation.STAR_CLOUDPRNT}/${translation.TO_DEVICE}/${macAddress}/${translation.PRINT_JOB}`

      const printJob = {
        title: translation.PRINT_JOB,
        jobToken: jobToken,
        jobType: JOB_TYPE,
        mediaTypes: [MEDIA_TYPE],
        printData: printData,
      }

      const response = await publishToTopicWithPromise(topic, printJob)
      return !!response
    } catch (error) {
      console.error('MQTT test print failed:', error)
      return false
    }
  }

  private async sendDirectNetworkPrint(
    printer: DiscoveredPrinter,
    printData: string
  ): Promise<boolean> {
    try {
      const { ipAddress, port } = printer.connectionDetails
      if (!ipAddress) {
        throw new Error('No IP address available for network printing')
      }

      // Decode base64 print data
      const binaryData = atob(printData)
      const uint8Array = new Uint8Array(binaryData.length)
      for (let i = 0; i < binaryData.length; i++) {
        uint8Array[i] = binaryData.charCodeAt(i)
      }

      // Try to send via fetch (limited browser support)
      const response = await fetch(`http://${ipAddress}:${port || 9100}/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/octet-stream',
        },
        body: uint8Array,
        mode: 'no-cors',
      })

      return true // If no error thrown, assume success
    } catch (error) {
      console.error('Direct network print failed:', error)
      return false
    }
  }

  private async sendUSBTestPrint(
    printer: DiscoveredPrinter,
    printData: string
  ): Promise<boolean> {
    try {
      if (typeof navigator === 'undefined' || !navigator.usb) {
        throw new Error('WebUSB not supported')
      }

      // Find the USB device
      const devices = await navigator.usb.getDevices()
      const device = devices.find(
        (d) =>
          d.vendorId === printer.connectionDetails.usbVendorId &&
          d.productId === printer.connectionDetails.usbProductId
      )

      if (!device) {
        throw new Error('USB device not found')
      }

      // Open device and send data
      await device.open()
      await device.selectConfiguration(1)
      await device.claimInterface(0)

      // Decode base64 print data
      const binaryData = atob(printData)
      const uint8Array = new Uint8Array(binaryData.length)
      for (let i = 0; i < binaryData.length; i++) {
        uint8Array[i] = binaryData.charCodeAt(i)
      }

      // Send data to printer (endpoint 1 is common for printers)
      await device.transferOut(1, uint8Array)

      // Close device
      await device.close()

      return true
    } catch (error) {
      console.error('USB test print failed:', error)
      return false
    }
  }

  private async sendBluetoothTestPrint(
    printer: DiscoveredPrinter,
    printData: string
  ): Promise<boolean> {
    try {
      if (typeof navigator === 'undefined' || !navigator.bluetooth) {
        throw new Error('Web Bluetooth not supported')
      }

      // This is a simplified implementation
      // In practice, you'd need to connect to the specific Bluetooth device
      // and use the appropriate service/characteristic for printing

      console.warn('Bluetooth test printing not fully implemented')
      return false
    } catch (error) {
      console.error('Bluetooth test print failed:', error)
      return false
    }
  }
}

export default PrinterDiscoveryService.getInstance()
