/**
 * Printer Discovery MQTT Service
 * Enhanced MQTT integration for printer discovery and management
 */

import {
  DiscoveredPrinter,
  PrinterConnectionMethod,
  PrinterDiscoveryStatus,
  PrinterTestResult,
  EpsonTestPrintData,
} from '@/types/module/printersModule'
import { translation } from '@/utils/translation'
import { generateUniqueAlphanumeric } from '@/utils/strings'
import { JOB_TYPE, MEDIA_TYPE } from '@/utils/constant'

export interface PrinterDiscoveryMQTTTopics {
  // Discovery topics
  discoveryRequest: string
  discoveryResponse: string
  
  // Printer status topics
  printerStatus: string
  printerStatusResponse: string
  
  // Test print topics
  testPrintRequest: string
  testPrintResponse: string
  
  // Printer management topics
  printerConnect: string
  printerDisconnect: string
  printerList: string
}

export interface PrinterDiscoveryRequest {
  requestId: string
  connectionMethods: PrinterConnectionMethod[]
  epsonOnly?: boolean
  timeout?: number
  storeId?: number
}

export interface PrinterDiscoveryResponse {
  requestId: string
  success: boolean
  printers: DiscoveredPrinter[]
  errors?: string[]
  timestamp: Date
}

export interface PrinterStatusRequest {
  requestId: string
  printerId?: string
  macAddress?: string
}

export interface PrinterStatusResponse {
  requestId: string
  printerId: string
  status: PrinterDiscoveryStatus
  isOnline: boolean
  lastSeen: Date
  errorMessage?: string
}

export interface TestPrintRequest {
  requestId: string
  printerId: string
  macAddress?: string
  testData: EpsonTestPrintData
  printData: string
}

export interface TestPrintResponse {
  requestId: string
  printerId: string
  success: boolean
  message?: string
  error?: string
  timestamp: Date
}

export class PrinterDiscoveryMQTTService {
  private static instance: PrinterDiscoveryMQTTService
  private mqttClient: any = null
  private storeId: number | null = null
  private pendingRequests: Map<string, {
    resolve: (value: any) => void
    reject: (error: any) => void
    timeout: NodeJS.Timeout
  }> = new Map()

  private constructor() {}

  public static getInstance(): PrinterDiscoveryMQTTService {
    if (!PrinterDiscoveryMQTTService.instance) {
      PrinterDiscoveryMQTTService.instance = new PrinterDiscoveryMQTTService()
    }
    return PrinterDiscoveryMQTTService.instance
  }

  /**
   * Initialize MQTT service with client and store ID
   */
  public initialize(mqttClient: any, storeId: number): void {
    this.mqttClient = mqttClient
    this.storeId = storeId
    this.setupSubscriptions()
  }

  /**
   * Generate MQTT topics for printer discovery
   */
  public getTopics(): PrinterDiscoveryMQTTTopics {
    const basePrefix = `${translation.STAR_CLOUDPRNT}/discovery`
    const storePrefix = `${basePrefix}/store/${this.storeId}`
    
    return {
      discoveryRequest: `${storePrefix}/request`,
      discoveryResponse: `${storePrefix}/response`,
      printerStatus: `${storePrefix}/status/request`,
      printerStatusResponse: `${storePrefix}/status/response`,
      testPrintRequest: `${storePrefix}/test/request`,
      testPrintResponse: `${storePrefix}/test/response`,
      printerConnect: `${storePrefix}/connect`,
      printerDisconnect: `${storePrefix}/disconnect`,
      printerList: `${storePrefix}/list`,
    }
  }

  /**
   * Setup MQTT subscriptions for printer discovery
   */
  private setupSubscriptions(): void {
    if (!this.mqttClient) return

    const topics = this.getTopics()
    
    // Subscribe to response topics
    this.mqttClient.subscribe(topics.discoveryResponse)
    this.mqttClient.subscribe(topics.printerStatusResponse)
    this.mqttClient.subscribe(topics.testPrintResponse)

    // Handle incoming messages
    this.mqttClient.on('message', (topic: string, payload: Buffer) => {
      try {
        const message = JSON.parse(payload.toString())
        this.handleMQTTMessage(topic, message)
      } catch (error) {
        console.error('Failed to parse MQTT message:', error)
      }
    })
  }

  /**
   * Handle incoming MQTT messages
   */
  private handleMQTTMessage(topic: string, message: any): void {
    const topics = this.getTopics()
    
    switch (topic) {
      case topics.discoveryResponse:
        this.handleDiscoveryResponse(message as PrinterDiscoveryResponse)
        break
      case topics.printerStatusResponse:
        this.handleStatusResponse(message as PrinterStatusResponse)
        break
      case topics.testPrintResponse:
        this.handleTestPrintResponse(message as TestPrintResponse)
        break
    }
  }

  /**
   * Request printer discovery via MQTT
   */
  public async requestPrinterDiscovery(
    connectionMethods: PrinterConnectionMethod[],
    options: {
      epsonOnly?: boolean
      timeout?: number
    } = {}
  ): Promise<PrinterDiscoveryResponse> {
    if (!this.mqttClient || !this.storeId) {
      throw new Error('MQTT service not initialized')
    }

    const requestId = generateUniqueAlphanumeric(8)
    const topics = this.getTopics()
    
    const request: PrinterDiscoveryRequest = {
      requestId,
      connectionMethods,
      epsonOnly: options.epsonOnly ?? true,
      timeout: options.timeout ?? 10000,
      storeId: this.storeId,
    }

    return new Promise((resolve, reject) => {
      // Set up timeout
      const timeout = setTimeout(() => {
        this.pendingRequests.delete(requestId)
        reject(new Error('Printer discovery request timeout'))
      }, (options.timeout ?? 10000) + 5000) // Add 5s buffer

      // Store pending request
      this.pendingRequests.set(requestId, { resolve, reject, timeout })

      // Publish discovery request
      this.mqttClient.publish(topics.discoveryRequest, JSON.stringify(request))
    })
  }

  /**
   * Request printer status via MQTT
   */
  public async requestPrinterStatus(
    printerId: string,
    macAddress?: string
  ): Promise<PrinterStatusResponse> {
    if (!this.mqttClient) {
      throw new Error('MQTT service not initialized')
    }

    const requestId = generateUniqueAlphanumeric(8)
    const topics = this.getTopics()
    
    const request: PrinterStatusRequest = {
      requestId,
      printerId,
      macAddress,
    }

    return new Promise((resolve, reject) => {
      // Set up timeout
      const timeout = setTimeout(() => {
        this.pendingRequests.delete(requestId)
        reject(new Error('Printer status request timeout'))
      }, 5000)

      // Store pending request
      this.pendingRequests.set(requestId, { resolve, reject, timeout })

      // Publish status request
      this.mqttClient.publish(topics.printerStatus, JSON.stringify(request))
    })
  }

  /**
   * Send test print via MQTT
   */
  public async sendTestPrint(
    printer: DiscoveredPrinter,
    testData: EpsonTestPrintData,
    printData: string
  ): Promise<TestPrintResponse> {
    if (!this.mqttClient) {
      throw new Error('MQTT service not initialized')
    }

    const requestId = generateUniqueAlphanumeric(8)
    const topics = this.getTopics()
    
    const request: TestPrintRequest = {
      requestId,
      printerId: printer.id,
      macAddress: printer.connectionDetails.macAddress,
      testData,
      printData,
    }

    return new Promise((resolve, reject) => {
      // Set up timeout
      const timeout = setTimeout(() => {
        this.pendingRequests.delete(requestId)
        reject(new Error('Test print request timeout'))
      }, 15000)

      // Store pending request
      this.pendingRequests.set(requestId, { resolve, reject, timeout })

      // Publish test print request
      this.mqttClient.publish(topics.testPrintRequest, JSON.stringify(request))
    })
  }

  /**
   * Handle discovery response
   */
  private handleDiscoveryResponse(response: PrinterDiscoveryResponse): void {
    const pending = this.pendingRequests.get(response.requestId)
    if (pending) {
      clearTimeout(pending.timeout)
      this.pendingRequests.delete(response.requestId)
      pending.resolve(response)
    }
  }

  /**
   * Handle status response
   */
  private handleStatusResponse(response: PrinterStatusResponse): void {
    const pending = this.pendingRequests.get(response.requestId)
    if (pending) {
      clearTimeout(pending.timeout)
      this.pendingRequests.delete(response.requestId)
      pending.resolve(response)
    }
  }

  /**
   * Handle test print response
   */
  private handleTestPrintResponse(response: TestPrintResponse): void {
    const pending = this.pendingRequests.get(response.requestId)
    if (pending) {
      clearTimeout(pending.timeout)
      this.pendingRequests.delete(response.requestId)
      pending.resolve(response)
    }
  }

  /**
   * Send legacy print job (for backward compatibility)
   */
  public async sendLegacyPrintJob(
    macAddress: string,
    printData: string
  ): Promise<boolean> {
    if (!this.mqttClient) {
      throw new Error('MQTT service not initialized')
    }

    try {
      const jobToken = `DISCOVERY-${generateUniqueAlphanumeric(7)}`
      const topic = `${translation.STAR_CLOUDPRNT}/${translation.TO_DEVICE}/${macAddress}/${translation.PRINT_JOB}`
      
      const printJob = {
        title: translation.PRINT_JOB,
        jobToken: jobToken,
        jobType: JOB_TYPE,
        mediaTypes: [MEDIA_TYPE],
        printData: printData,
      }

      return new Promise((resolve, reject) => {
        this.mqttClient.publish(topic, JSON.stringify(printJob), (error: any) => {
          if (error) {
            reject(error)
          } else {
            resolve(true)
          }
        })
      })
    } catch (error) {
      console.error('Legacy print job failed:', error)
      return false
    }
  }

  /**
   * Cleanup pending requests
   */
  public cleanup(): void {
    for (const [requestId, pending] of this.pendingRequests) {
      clearTimeout(pending.timeout)
      pending.reject(new Error('Service cleanup'))
    }
    this.pendingRequests.clear()
  }
}

export default PrinterDiscoveryMQTTService.getInstance()
