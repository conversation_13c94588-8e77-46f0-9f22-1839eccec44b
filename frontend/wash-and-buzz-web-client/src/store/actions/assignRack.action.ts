import {
  AssignRackData,
  AssignRackPayload,
  GetAssignRackDataProps,
} from '@/types/module/assignRackModule'
import { TicketValidationResult } from '@/types/module/customerTicketsModule'
import {
  AssignRackDataFailure,
  AssignRackDataSuccess,
  GetAssignRackSuccess,
} from '@/types/store/actions/assignRack.action'
import {
  ASSIGN_RACK_DATA,
  ASSIGN_RACK_DATA_SUCCESS,
  CHECK_DATA_VALIDATION,
  CLEAR_ASSIGN_RACK_DATA,
  GET_ASSIGN_RACK_DATA,
  GET_ASSIGN_RACK_DATA_SUCCESS,
  GET_ASSIGN_RACK_FAILURE,
  UPDATE_RACK_PAGINATION,
} from '@/utils/storeTypes'
import { AxiosError } from 'axios'

export const getAssignRackDataFailureAction = (
  error: AxiosError | Error
): AssignRackDataFailure => ({
  type: GET_ASSIGN_RACK_FAILURE,
  error,
})

export const clearAssignRackDataAction = (
  payload: null,
  callBack?: (res: boolean) => void
) => {
  return {
    type: CLEAR_ASSIGN_RACK_DATA,
    payload,
    callBack,
  }
}

export const getAssignRackDataAction = (
  payload: GetAssignRackDataProps,
  callBack?: (res: boolean) => void
) => {
  return {
    type: GET_ASSIGN_RACK_DATA,
    payload,
    callBack,
  }
}
export const getAssignRackActionSuccess = (
  data: AssignRackData[]
): GetAssignRackSuccess => ({
  type: GET_ASSIGN_RACK_DATA_SUCCESS,
  payload: data,
})

export const assignRackDataAction = (
  payload: AssignRackPayload,
  callBack?: (res: boolean, error?: string) => void
) => ({
  type: ASSIGN_RACK_DATA,
  payload,
  callBack,
})

export const assignRackDataSuccessAction = (
  data: AssignRackData[]
): AssignRackDataSuccess => ({
  type: ASSIGN_RACK_DATA_SUCCESS,
  payload: data,
})

export const updateRackPaginationData = (hasMoreData: boolean) => ({
  type: UPDATE_RACK_PAGINATION,
  hasMoreData,
})

export const checkDataValidation = (
  ticketId: number,
  rackId: string,
  callBack: (result: TicketValidationResult) => void
) => ({
  type: CHECK_DATA_VALIDATION,
  ticketId,
  rackId,
  callBack,
})
