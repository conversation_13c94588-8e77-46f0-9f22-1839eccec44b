import assignRackOperations from '@/persistence/idb/assignRack.repo'
import {
  assignRackDataSuccessAction,
  getAssignRackActionSuccess,
  getAssignRackDataFailureAction,
  updateRackPaginationData,
} from '@/store/actions/assignRack.action'
import { AssignRackData<PERSON>pi, getAssignRackDataApi } from '@/store/apis'
import { AssignRackData } from '@/types/module/assignRackModule'
import { CommonIdbResponse, IdbResponse } from '@/types/module/commonModule'
import {
  TicketValidationResult,
  TicketValidationStatus,
} from '@/types/module/customerTicketsModule'
import {
  AssignRackDataAction,
  CheckDataValidation,
  GetAssignRackData,
} from '@/types/store/actions/assignRack.action'
import { AssignRackDataState } from '@/types/store/reducers/assignRack.reducer'
import { StoreData } from '@/types/store/reducers/store.reducers'
import { ASSIGN_RACK_CONTINUATION_TOKEN } from '@/utils/constant'
import { computeCookieExpiry } from '@/utils/functions'
import { assignRackData, storeData } from '@/utils/selectors'
import {
  ASSIGN_RACK_DATA,
  CHECK_DATA_VALIDATION,
  GET_ASSIGN_RACK_DATA,
} from '@/utils/storeTypes'
import { translation } from '@/utils/translation'
import { AxiosError, AxiosResponse } from 'axios'
import Cookies from 'js-cookie'
import { call, put, select, takeLatest } from 'redux-saga/effects'

function* fetchAssignRackDataWorker(action: GetAssignRackData) {
  try {
    const mainStoreData: StoreData = yield select(storeData)

    if (!mainStoreData?.data?.id) return

    const { storeId } = action.payload

    const modifiedContinuationToken =
      action.payload.continuationToken?.replace(/^"|"$/g, '') || null

    const response: AxiosResponse = yield call(
      getAssignRackDataApi,
      storeId as number,
      modifiedContinuationToken
    )

    if (response?.status === 200) {
      const assignRackResponseData = response?.data?.data
      const continuationTokenData = assignRackResponseData?.continuationToken

      if (continuationTokenData) {
        const urlEncodedContinuationToken = encodeURIComponent(
          continuationTokenData
        )
        Cookies.set(
          ASSIGN_RACK_CONTINUATION_TOKEN,
          urlEncodedContinuationToken,
          computeCookieExpiry()
        )
        yield put(updateRackPaginationData(true))
      } else {
        const getAssignRackData: AssignRackDataState =
          yield select(assignRackData)
        if (
          getAssignRackData.rackAssignments &&
          getAssignRackData.rackAssignments?.length > 0
        ) {
          Cookies.remove(ASSIGN_RACK_CONTINUATION_TOKEN)
          yield put(updateRackPaginationData(false))
        }
      }

      const deleteAllAssignRacksResponse: IdbResponse<AssignRackData[]> =
        yield call(assignRackOperations.deleteAllAssignRacks)

      if (deleteAllAssignRacksResponse.success) {
        const addAssignRackData = () =>
          assignRackOperations.addMultipleAssignRacks(
            assignRackResponseData?.rackAssignments
          )

        const idbResponse: IdbResponse<AssignRackData[]> =
          yield call(addAssignRackData)

        if (idbResponse.success) {
          yield put(
            getAssignRackActionSuccess(assignRackResponseData?.rackAssignments)
          )
          action?.callBack(true)
          return
        } else {
          if (idbResponse.error instanceof Error) {
            yield put(getAssignRackDataFailureAction(idbResponse.error))
            action?.callBack(false)
            return
          }
        }
      }
    }
  } catch (error) {
    if (error instanceof Error || error instanceof AxiosError) {
      yield put(getAssignRackDataFailureAction(error))
      action?.callBack(false)
    }
  }
}

function* AssignRackDataWorker(action: AssignRackDataAction) {
  try {
    const mainStoreData: StoreData = yield select(storeData)
    // const ticketsData: CustomerTicketsState = yield select(customerTicketData) // TODO

    if (!mainStoreData?.data?.id) return

    const response: AxiosResponse = yield call(
      AssignRackDataApi,
      action.payload
    )

    if (response?.status === 200) {
      const addAssignRackData = () =>
        assignRackOperations.addAssignRack(response.data.data?.[0])
      const idbResponse: CommonIdbResponse = yield call(addAssignRackData)

      if (idbResponse.success) {
        yield put(assignRackDataSuccessAction(response.data.data))
        action?.callBack(true)
      }
    }
  } catch (error) {
    if (error instanceof AxiosError) {
      action.callBack(
        false,
        error.response?.data?.messages?.[0] || error.message || ''
      )
      yield put(getAssignRackDataFailureAction(error))
    }
  }
}
function* CheckDataValidationDataWorker(action: CheckDataValidation) {
  try {
    const mainStoreData: StoreData = yield select(storeData)

    if (!mainStoreData?.data?.id) return

    const checkDataValidation = (ticketId: number, rackId: string) =>
      assignRackOperations.validateTicketAndRackData(
        ticketId,
        rackId,
        mainStoreData.data?.fulfillmentSettings?.rackManagement
          .allowMultiTicketRackAssignment || false
      )

    const response: Promise<TicketValidationResult> = yield call(
      checkDataValidation,
      action.ticketId,
      action.rackId
    )

    const validationResult: TicketValidationResult = yield response
    action.callBack(validationResult)
  } catch (error) {
    if (error instanceof AxiosError) {
      action.callBack({
        status: TicketValidationStatus.InvalidTicket,
        message: translation.UNEXPECTED_ERROR_OCCURRED,
      })
      yield put(getAssignRackDataFailureAction(error))
    }
  }
}

// ADD_ASSIGN_RACK_DATA

export function* assignRackDataApiSaga() {
  yield takeLatest(GET_ASSIGN_RACK_DATA, fetchAssignRackDataWorker)
  yield takeLatest(ASSIGN_RACK_DATA, AssignRackDataWorker)
  yield takeLatest(CHECK_DATA_VALIDATION, CheckDataValidationDataWorker)
}
