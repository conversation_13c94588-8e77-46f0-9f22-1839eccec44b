/**
 * Printer Discovery Service Tests
 * Comprehensive test suite for printer discovery functionality
 */

import { describe, it, expect, beforeEach, afterEach, vi, Mock } from 'vitest'
import PrinterDiscoveryService from '@/services/printerDiscovery.service'
import {
  PrinterConnectionMethod,
  PrinterDiscoveryOptions,
  PrinterDiscoveryStatus,
  DiscoveredPrinter,
} from '@/types/module/printersModule'

// Mock browser APIs
const mockUSBDevice = {
  vendorId: 0x04b8,
  productId: 0x0202,
  productName: 'TM-T88VI',
  manufacturerName: 'Epson',
  serialNumber: 'TEST123',
  opened: false,
  open: vi.fn().mockResolvedValue(undefined),
  close: vi.fn().mockResolvedValue(undefined),
}

const mockBluetoothDevice = {
  id: 'bluetooth-test-device',
  name: 'EPSON TM-m30',
  gatt: {
    connect: vi.fn().mockResolvedValue({
      getPrimaryService: vi.fn().mockResolvedValue({
        getCharacteristics: vi.fn().mockResolvedValue([]),
      }),
      disconnect: vi.fn().mockResolvedValue(undefined),
    }),
  },
}

// Mock global APIs
Object.defineProperty(global, 'navigator', {
  value: {
    usb: {
      getDevices: vi.fn(),
      requestDevice: vi.fn(),
    },
    bluetooth: {
      getDevices: vi.fn(),
      requestDevice: vi.fn(),
    },
    onLine: true,
  },
  writable: true,
})

Object.defineProperty(global, 'fetch', {
  value: vi.fn(),
  writable: true,
})

Object.defineProperty(global, 'WebSocket', {
  value: vi.fn(),
  writable: true,
})

describe('PrinterDiscoveryService', () => {
  let service: PrinterDiscoveryService
  let mockFetch: Mock
  let mockUSBGetDevices: Mock
  let mockUSBRequestDevice: Mock
  let mockBluetoothGetDevices: Mock
  let mockBluetoothRequestDevice: Mock

  beforeEach(() => {
    service = PrinterDiscoveryService.getInstance()
    service.clearCache()

    mockFetch = global.fetch as Mock
    mockUSBGetDevices = navigator.usb.getDevices as Mock
    mockUSBRequestDevice = navigator.usb.requestDevice as Mock
    mockBluetoothGetDevices = navigator.bluetooth.getDevices as Mock
    mockBluetoothRequestDevice = navigator.bluetooth.requestDevice as Mock

    // Reset all mocks
    vi.clearAllMocks()
  })

  afterEach(() => {
    service.stopDiscovery()
    service.clearCache()
  })

  describe('Network Discovery', () => {
    it('should discover network printers successfully', async () => {
      const options: PrinterDiscoveryOptions = {
        connectionMethods: [PrinterConnectionMethod.NETWORK],
        timeout: 1000,
        epsonOnly: true,
      }

      mockFetch.mockResolvedValue({
        ok: true,
        status: 200,
      })

      const result = await service.discoverPrinters(options)

      expect(result.success).toBe(true)
      expect(result.printers).toBeDefined()
      expect(result.scanDuration).toBeGreaterThan(0)
    })

    it('should handle network discovery timeout', async () => {
      const options: PrinterDiscoveryOptions = {
        connectionMethods: [PrinterConnectionMethod.NETWORK],
        timeout: 100,
        epsonOnly: true,
      }

      mockFetch.mockImplementation(() => 
        new Promise(resolve => setTimeout(resolve, 200))
      )

      const result = await service.discoverPrinters(options)

      expect(result.success).toBe(true)
      expect(result.errors).toBeDefined()
    })
  })

  describe('USB Discovery', () => {
    it('should discover USB printers successfully', async () => {
      mockUSBGetDevices.mockResolvedValue([mockUSBDevice])

      const options: PrinterDiscoveryOptions = {
        connectionMethods: [PrinterConnectionMethod.USB],
        epsonOnly: true,
      }

      const result = await service.discoverPrinters(options)

      expect(result.success).toBe(true)
      expect(result.printers).toHaveLength(1)
      expect(result.printers[0].name).toBe('TM-T88VI')
      expect(result.printers[0].manufacturer).toBe('Epson')
      expect(result.printers[0].connectionMethod).toBe(PrinterConnectionMethod.USB)
    })

    it('should request new USB device when none paired', async () => {
      mockUSBGetDevices.mockResolvedValue([])
      mockUSBRequestDevice.mockResolvedValue(mockUSBDevice)

      const options: PrinterDiscoveryOptions = {
        connectionMethods: [PrinterConnectionMethod.USB],
        epsonOnly: true,
      }

      const result = await service.discoverPrinters(options)

      expect(mockUSBRequestDevice).toHaveBeenCalled()
      expect(result.success).toBe(true)
      expect(result.printers).toHaveLength(1)
    })

    it('should handle USB not supported error', async () => {
      // Temporarily remove USB support
      const originalUSB = navigator.usb
      delete (navigator as any).usb

      const options: PrinterDiscoveryOptions = {
        connectionMethods: [PrinterConnectionMethod.USB],
        epsonOnly: true,
      }

      const result = await service.discoverPrinters(options)

      expect(result.success).toBe(true)
      expect(result.errors).toContain('USB: WebUSB not supported in this browser')

      // Restore USB support
      ;(navigator as any).usb = originalUSB
    })

    it('should handle USB permission denied', async () => {
      mockUSBGetDevices.mockResolvedValue([])
      mockUSBRequestDevice.mockRejectedValue(new DOMException('Permission denied', 'NotAllowedError'))

      const options: PrinterDiscoveryOptions = {
        connectionMethods: [PrinterConnectionMethod.USB],
        epsonOnly: true,
      }

      const result = await service.discoverPrinters(options)

      expect(result.success).toBe(true)
      expect(result.printers).toHaveLength(0)
    })
  })

  describe('Bluetooth Discovery', () => {
    it('should discover Bluetooth printers successfully', async () => {
      mockBluetoothGetDevices.mockResolvedValue([mockBluetoothDevice])

      const options: PrinterDiscoveryOptions = {
        connectionMethods: [PrinterConnectionMethod.BLUETOOTH],
        epsonOnly: true,
      }

      const result = await service.discoverPrinters(options)

      expect(result.success).toBe(true)
      expect(result.printers).toHaveLength(1)
      expect(result.printers[0].name).toBe('EPSON TM-m30')
      expect(result.printers[0].connectionMethod).toBe(PrinterConnectionMethod.BLUETOOTH)
    })

    it('should request new Bluetooth device when none paired', async () => {
      mockBluetoothGetDevices.mockRejectedValue(new Error('getDevices not supported'))
      mockBluetoothRequestDevice.mockResolvedValue(mockBluetoothDevice)

      const options: PrinterDiscoveryOptions = {
        connectionMethods: [PrinterConnectionMethod.BLUETOOTH],
        epsonOnly: true,
      }

      const result = await service.discoverPrinters(options)

      expect(mockBluetoothRequestDevice).toHaveBeenCalled()
      expect(result.success).toBe(true)
      expect(result.printers).toHaveLength(1)
    })

    it('should handle Bluetooth not supported error', async () => {
      // Temporarily remove Bluetooth support
      const originalBluetooth = navigator.bluetooth
      delete (navigator as any).bluetooth

      const options: PrinterDiscoveryOptions = {
        connectionMethods: [PrinterConnectionMethod.BLUETOOTH],
        epsonOnly: true,
      }

      const result = await service.discoverPrinters(options)

      expect(result.success).toBe(true)
      expect(result.errors).toContain('BLUETOOTH: Web Bluetooth not supported in this browser')

      // Restore Bluetooth support
      ;(navigator as any).bluetooth = originalBluetooth
    })
  })

  describe('Test Printing', () => {
    it('should generate test print data successfully', async () => {
      const printer: DiscoveredPrinter = {
        id: 'test-printer',
        name: 'Test Printer',
        manufacturer: 'Epson',
        connectionMethod: PrinterConnectionMethod.NETWORK,
        connectionDetails: {
          ipAddress: '*************',
          port: 9100,
          macAddress: 'AA:BB:CC:DD:EE:FF',
        },
        capabilities: {
          supportsEpson: true,
          supportedLanguages: ['ESC/POS'],
        },
        status: PrinterDiscoveryStatus.FOUND,
        lastSeen: new Date(),
        isOnline: true,
      }

      const testData = {
        storeName: 'Test Store',
        storeAddress: 'Test Address',
        testPattern: true,
        includeBarcode: true,
        includeQRCode: true,
        customText: 'Test print',
      }

      // Mock successful print
      mockFetch.mockResolvedValue({ ok: true })

      const result = await service.testPrint(printer, testData)

      expect(result.success).toBe(true)
      expect(result.printerId).toBe(printer.id)
      expect(result.timestamp).toBeInstanceOf(Date)
    })

    it('should handle test print failure', async () => {
      const printer: DiscoveredPrinter = {
        id: 'test-printer',
        name: 'Test Printer',
        manufacturer: 'Epson',
        connectionMethod: PrinterConnectionMethod.NETWORK,
        connectionDetails: {
          ipAddress: '*************',
          port: 9100,
        },
        capabilities: {
          supportsEpson: true,
          supportedLanguages: ['ESC/POS'],
        },
        status: PrinterDiscoveryStatus.FOUND,
        lastSeen: new Date(),
        isOnline: true,
      }

      const testData = {
        storeName: 'Test Store',
        storeAddress: 'Test Address',
        testPattern: false,
        includeBarcode: false,
        includeQRCode: false,
      }

      // Mock failed print
      mockFetch.mockRejectedValue(new Error('Network error'))

      const result = await service.testPrint(printer, testData)

      expect(result.success).toBe(false)
      expect(result.error).toBeDefined()
    })
  })

  describe('Service Management', () => {
    it('should prevent concurrent discovery operations', async () => {
      const options: PrinterDiscoveryOptions = {
        connectionMethods: [PrinterConnectionMethod.NETWORK],
        timeout: 1000,
      }

      // Start first discovery
      const promise1 = service.discoverPrinters(options)

      // Try to start second discovery
      await expect(service.discoverPrinters(options)).rejects.toThrow('Discovery already in progress')

      // Wait for first discovery to complete
      await promise1
    })

    it('should stop discovery operation', async () => {
      const options: PrinterDiscoveryOptions = {
        connectionMethods: [PrinterConnectionMethod.NETWORK],
        timeout: 5000,
      }

      // Start discovery
      const promise = service.discoverPrinters(options)

      // Stop discovery immediately
      service.stopDiscovery()

      const result = await promise

      expect(result.success).toBe(true)
    })

    it('should cache discovered printers', async () => {
      mockUSBGetDevices.mockResolvedValue([mockUSBDevice])

      const options: PrinterDiscoveryOptions = {
        connectionMethods: [PrinterConnectionMethod.USB],
        epsonOnly: true,
      }

      await service.discoverPrinters(options)

      const cachedPrinters = service.getDiscoveredPrinters()
      expect(cachedPrinters).toHaveLength(1)
      expect(cachedPrinters[0].name).toBe('TM-T88VI')
    })

    it('should clear printer cache', async () => {
      mockUSBGetDevices.mockResolvedValue([mockUSBDevice])

      const options: PrinterDiscoveryOptions = {
        connectionMethods: [PrinterConnectionMethod.USB],
        epsonOnly: true,
      }

      await service.discoverPrinters(options)
      expect(service.getDiscoveredPrinters()).toHaveLength(1)

      service.clearCache()
      expect(service.getDiscoveredPrinters()).toHaveLength(0)
    })
  })
})
