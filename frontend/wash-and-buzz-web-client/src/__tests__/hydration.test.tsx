/**
 * Hydration Tests
 * Tests to ensure components render consistently between server and client
 */

import { describe, it, expect, beforeEach, vi } from 'vitest'
import { render, screen } from '@testing-library/react'
import { Provider } from 'react-redux'
import { configureStore } from '@reduxjs/toolkit'
import ConnectionMethodSelector from '@/components/PrinterDiscovery/ConnectionMethodSelector'
import ClientOnlyWrapper from '@/components/PrinterDiscovery/ClientOnlyWrapper'
import { PrinterConnectionMethod } from '@/types/module/printersModule'

// Mock store
const mockStore = configureStore({
  reducer: {
    storeData: () => ({ data: { id: 1, name: 'Test Store' } }),
    printersData: () => ({ data: [] }),
  },
})

// Mock navigator APIs
Object.defineProperty(global, 'navigator', {
  value: {
    usb: undefined,
    bluetooth: undefined,
    onLine: true,
  },
  writable: true,
})

describe('Hydration Tests', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('ClientOnlyWrapper', () => {
    it('should render fallback on server side', () => {
      render(
        <ClientOnlyWrapper fallback={<div>Loading...</div>}>
          <div>Client content</div>
        </ClientOnlyWrapper>
      )

      // Should show fallback initially
      expect(screen.getByText('Loading...')).toBeInTheDocument()
    })

    it('should render children after mounting', async () => {
      const { rerender } = render(
        <ClientOnlyWrapper>
          <div>Client content</div>
        </ClientOnlyWrapper>
      )

      // Initially shows loading
      expect(screen.getByText('Loading printer discovery...')).toBeInTheDocument()

      // Simulate client-side hydration
      rerender(
        <ClientOnlyWrapper>
          <div>Client content</div>
        </ClientOnlyWrapper>
      )

      // After hydration, should show content
      // Note: In actual usage, useEffect would trigger this change
    })
  })

  describe('ConnectionMethodSelector', () => {
    const defaultProps = {
      selectedMethods: [PrinterConnectionMethod.NETWORK],
      onMethodToggle: vi.fn(),
      disabled: false,
    }

    it('should render without browser APIs available', () => {
      // Ensure navigator APIs are not available
      Object.defineProperty(global, 'navigator', {
        value: {
          usb: undefined,
          bluetooth: undefined,
          onLine: undefined,
        },
        writable: true,
      })

      render(<ConnectionMethodSelector {...defaultProps} />)

      // Should render without errors
      expect(screen.getByText('Network Scan')).toBeInTheDocument()
      expect(screen.getByText('USB Connection')).toBeInTheDocument()
      expect(screen.getByText('Bluetooth')).toBeInTheDocument()
    })

    it('should handle browser capability detection gracefully', () => {
      // Mock partial browser support
      Object.defineProperty(global, 'navigator', {
        value: {
          usb: { getDevices: vi.fn() },
          bluetooth: undefined,
          onLine: true,
        },
        writable: true,
      })

      render(<ConnectionMethodSelector {...defaultProps} />)

      // Should render all methods regardless of initial browser state
      expect(screen.getByText('Network Scan')).toBeInTheDocument()
      expect(screen.getByText('USB Connection')).toBeInTheDocument()
      expect(screen.getByText('Bluetooth')).toBeInTheDocument()
      expect(screen.getByText('Ethernet')).toBeInTheDocument()
    })

    it('should show browser compatibility info', () => {
      render(<ConnectionMethodSelector {...defaultProps} />)

      expect(screen.getByText('Browser Compatibility:')).toBeInTheDocument()
      expect(screen.getByText(/Network\/Ethernet.*Supported in all modern browsers/)).toBeInTheDocument()
      expect(screen.getByText(/USB.*Requires Chrome\/Edge with WebUSB support/)).toBeInTheDocument()
    })
  })

  describe('Server-Client Consistency', () => {
    it('should render consistently between server and client', () => {
      // Simulate server-side rendering (no browser APIs)
      Object.defineProperty(global, 'navigator', {
        value: undefined,
        writable: true,
      })

      const serverRender = render(
        <Provider store={mockStore}>
          <ClientOnlyWrapper>
            <ConnectionMethodSelector
              selectedMethods={[PrinterConnectionMethod.NETWORK]}
              onMethodToggle={vi.fn()}
              disabled={false}
            />
          </ClientOnlyWrapper>
        </Provider>
      )

      // Should render loading state
      expect(screen.getByText('Loading printer discovery...')).toBeInTheDocument()

      // Simulate client-side hydration
      Object.defineProperty(global, 'navigator', {
        value: {
          usb: { getDevices: vi.fn() },
          bluetooth: { requestDevice: vi.fn() },
          onLine: true,
        },
        writable: true,
      })

      // Re-render should not cause hydration mismatch
      serverRender.rerender(
        <Provider store={mockStore}>
          <ClientOnlyWrapper>
            <ConnectionMethodSelector
              selectedMethods={[PrinterConnectionMethod.NETWORK]}
              onMethodToggle={vi.fn()}
              disabled={false}
            />
          </ClientOnlyWrapper>
        </Provider>
      )

      // Should still work without errors
      expect(serverRender.container).toBeInTheDocument()
    })
  })
})

// Test utility to simulate hydration
export function simulateHydration() {
  // Simulate the transition from server to client
  Object.defineProperty(global, 'navigator', {
    value: {
      usb: { getDevices: vi.fn() },
      bluetooth: { requestDevice: vi.fn() },
      onLine: true,
    },
    writable: true,
  })
}
