import {
  add,
  addMultiple,
  clearTable,
  getAll,
  validateTicketAndRackAssignment,
} from '@/app/db'
import { CUSTOMER_TICKETS_TABLE_NAME } from '@/persistence/idb/customerTickets.repo'
import { AssignRackData } from '@/types/module/assignRackModule'
import { AssignRackDbOperations } from '@/types/persistence/assignRack.repo.interface'
import { TICKET_ID } from '@/utils/constant'

const ASSIGN_RACKS_TABLE_NAME = 'assignRacks'

const assignRackOperations: AssignRackDbOperations = {
  getAllAssignRacks: () => getAll<AssignRackData>(ASSIGN_RACKS_TABLE_NAME),
  addAssignRack: (ticket) =>
    add<AssignRackData>(ASSIGN_RACKS_TABLE_NAME, ticket),
  addMultipleAssignRacks: (item) =>
    addMultiple<AssignRackData>(ASSIGN_RACKS_TABLE_NAME, item),
  deleteAllAssignRacks: () => clearTable(ASSIGN_RACKS_TABLE_NAME),
  validateTicketAndRackData: (
    ticketId: number,
    rackId: string,
    multipleRackAssignment: boolean
  ) =>
    validateTicketAndRackAssignment(
      CUSTOMER_TICKETS_TABLE_NAME,
      TICKET_ID,
      ticketId,
      rackId,
      multipleRackAssignment
    ),
}
export default assignRackOperations
