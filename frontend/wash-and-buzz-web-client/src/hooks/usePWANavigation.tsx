'use client'

import { useCallback } from 'react'
import { isPWA } from '@/utils/pwaNavigation'

/**
 * Simple PWA Navigation Hook
 * Opens settings in new window for PWA, new tab for browser
 */
export const usePWANavigation = () => {
  /**
   * Open settings page with appropriate behavior
   * In PWA mode, opens in new window
   * In browser mode, opens in new tab
   */
  const openSettings = useCallback((settingsPath: string) => {
    const runningAsPWA = isPWA()

    if (runningAsPWA) {
      // In PWA mode, open in new window
      window.open(settingsPath, '_blank', 'noopener,noreferrer')
    } else {
      // In browser mode, open in new tab
      window.open(settingsPath, '_blank', 'noopener,noreferrer')
    }
  }, [])

  /**
   * Check if currently running as PWA
   */
  const isRunningAsPWA = useCallback(() => {
    return isPWA()
  }, [])

  return {
    openSettings,
    isRunningAsPWA,
    isPWA: isRunningAsPWA,
  }
}

export default usePWANavigation
