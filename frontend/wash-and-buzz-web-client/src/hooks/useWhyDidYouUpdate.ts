// Keep this custom hook for debugging the child component re-render due to the parent component props change.
import { useEffect, useRef } from 'react'

export function useWhyDidYouUpdate(componentName: string, props: any) {
  const previousProps = useRef(props)

  useEffect(() => {
    const changedProps: Record<string, { from: any; to: any }> = {}

    Object.keys(props).forEach((key) => {
      if (props[key] !== previousProps.current[key]) {
        changedProps[key] = {
          from: previousProps.current[key],
          to: props[key],
        }
      }
    })

    if (Object.keys(changedProps).length > 0) {
      console.log(`[🔁 ${componentName}] Props changed:`, changedProps)
    }

    previousProps.current = props
  })
}
