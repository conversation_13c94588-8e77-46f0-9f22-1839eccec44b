import { usePathname, useRouter } from 'next/navigation'
import { useEffect, useRef } from 'react'

type Props = {
  shouldBlock: boolean
  onBlock: (triggeredByBackButton: boolean) => void
}

const useBlockNavigation = ({ shouldBlock, onBlock }: Props) => {
  const router = useRouter()
  const pathname = usePathname()
  const originalPushRef = useRef(router.push)
  const isBackNavigationRef = useRef(false)
  useEffect(() => {
    const handleNavigation = (url: string) => {
      if (!shouldBlock || url === pathname) {
        originalPushRef.current(url)
        return
      }

      isBackNavigationRef.current = false
      onBlock(false)
    }

    router.push = ((url) => {
      handleNavigation(url)
    }) as typeof router.push

    return () => {
      // eslint-disable-next-line
      router.push = originalPushRef.current
    }
    // eslint-disable-next-line
  }, [shouldBlock, pathname, onBlock])

  useEffect(() => {
    const handlePopState = (e: PopStateEvent) => {
      if (!shouldBlock) return

      e.preventDefault?.()

      isBackNavigationRef.current = true
      onBlock(true)

      history.pushState(null, '', window.location.href)
    }

    if (shouldBlock) {
      history.pushState(null, '', window.location.href)
    }

    window.addEventListener('popstate', handlePopState)

    return () => {
      window.removeEventListener('popstate', handlePopState)
    }
  }, [shouldBlock, onBlock])

  const cancelNavigation = () => {
    history.pushState(null, '', window.location.href)
  }

  return {
    cancelNavigation,
    originalPush: originalPushRef.current,
    wasTriggeredByBackButton: () => isBackNavigationRef.current,
  }
}

export default useBlockNavigation
