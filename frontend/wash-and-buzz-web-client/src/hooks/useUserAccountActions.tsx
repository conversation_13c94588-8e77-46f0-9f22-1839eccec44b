import { setIsIdeal } from '@/store/actions/common.action'
import {
  setOpenLeaveConfirmationModal,
  setRequestedAction,
  setRequestedRoute,
} from '@/store/actions/currentTicketState.action'
import { logout } from '@/store/actions/logout.action'
import { RequestedAction } from '@/types/module/ticketNdSalesModule'
import {
  ACCESS_TOKEN,
  FALSE,
  IS_PIN_MODAL_OPEN,
  IS_TEAM_MEMBER_SELECTED,
  TRUE,
} from '@/utils/constant'
import { computeCookieExpiry, setInitialStorageState } from '@/utils/functions'
import { appRoutes } from '@/utils/routes'
import Cookies from 'js-cookie'
import { signOut } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { useDispatch } from 'react-redux'
import { usePWANavigation } from '@/hooks/usePWANavigation'

export const useUserAccountActions = () => {
  const dispatch = useDispatch()
  const router = useRouter()
  const { openSettings } = usePWANavigation()
  const handleOptionClick = (action: RequestedAction) => {
    switch (action) {
      case RequestedAction.CHANGE_USER:
        Cookies.set(IS_TEAM_MEMBER_SELECTED, FALSE, computeCookieExpiry())
        localStorage.setItem(IS_PIN_MODAL_OPEN, TRUE)
        dispatch(setIsIdeal(true))
        break

      case RequestedAction.CHANGE_STORE:
        router.push(appRoutes.selectStoreData)
        break

      case RequestedAction.OPEN_SETTINGS:
        openSettings(appRoutes.storeDetails)
        break

      case RequestedAction.LOGOUT:
        const accessToken = Cookies.get(ACCESS_TOKEN)
        if (accessToken) {
          dispatch(
            logout(accessToken, async () => {
              setInitialStorageState()
              await signOut({ callbackUrl: appRoutes.home })
            })
          )
        }
        break
      default:
        break
    }
  }

  const handleOptionEditClick = (action: RequestedAction) => {
    let route = ''

    switch (action) {
      case RequestedAction.CHANGE_USER:
        route = appRoutes.tickets
        break

      case RequestedAction.CHANGE_STORE:
        route = appRoutes.selectStoreData
        break

      case RequestedAction.OPEN_SETTINGS:
        route = appRoutes.tickets
        break

      case RequestedAction.LOGOUT:
        route = appRoutes.home
        break

      default:
        return
    }

    dispatch(setOpenLeaveConfirmationModal(true))
    dispatch(setRequestedAction(action))
    dispatch(setRequestedRoute(route))
  }

  return {
    handleOptionClick,
    handleOptionEditClick,
  }
}
