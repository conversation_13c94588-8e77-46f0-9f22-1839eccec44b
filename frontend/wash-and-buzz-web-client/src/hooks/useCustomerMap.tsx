import { Customers } from '@/types/module/addCustomersModule'
import { MainStoreType } from '@/types/store/reducers/main.reducers'
import { useMemo } from 'react'
import { useSelector } from 'react-redux'

export const useCustomerMap = () => {
  const customers = useSelector(
    (state: MainStoreType) => state.customerData.data
  )

  const customerMap = useMemo(() => {
    const map = new Map<number, Customers>()
    customers?.forEach((customer) => {
      if (customer.storeCustomerId != null) {
        map.set(customer.storeCustomerId, customer)
      }
    })
    return map
  }, [customers])

  return customerMap
}
