import {
  getSelectedCustomersData,
  setSelectedCustomersData,
} from '@/store/actions/customer.action'
import { Customers } from '@/types/module/addCustomersModule'
import { CustomerDataState } from '@/types/store/reducers/customer.reducer'
import { SELECTED_CUSTOMER_SEARCH_DATA } from '@/utils/constant'
import { appRoutes } from '@/utils/routes'
import { useRouter } from 'next/navigation'
import { useCallback } from 'react'
import { useDispatch } from 'react-redux'

interface UseHandleClickCustomerProps {
  customers: CustomerDataState
  shouldRedirect?: boolean
}

export const useHandleClickCustomer = ({
  customers,
  shouldRedirect = false,
}: UseHandleClickCustomerProps) => {
  const dispatch = useDispatch()
  const route = useRouter()

  const handleClickCustomer = useCallback(
    (selectedID: number) => {
      const findCustomer = customers.data?.find(
        (customer) => customer.storeCustomerId === selectedID
      )

      if (!findCustomer) return

      const selectedCustomerID = customers?.selectedCustomer?.storeCustomerId

      dispatch(
        getSelectedCustomersData(
          { storeCustomerId: findCustomer.storeCustomerId },
          (res: boolean) => {
            if (res) {
              let storedCustomers: Customers[] = JSON.parse(
                localStorage.getItem(SELECTED_CUSTOMER_SEARCH_DATA) || '[]'
              )

              storedCustomers = storedCustomers.filter(
                (storedCustomer) =>
                  !(
                    storedCustomer.storeCustomerId ===
                      findCustomer.storeCustomerId &&
                    storedCustomer.searchMatchType ===
                      findCustomer.searchMatchType &&
                    storedCustomer.ticketNumber === findCustomer.ticketNumber
                  )
              )

              storedCustomers.unshift(findCustomer)
              if (storedCustomers.length > 5) storedCustomers.pop()

              localStorage.setItem(
                SELECTED_CUSTOMER_SEARCH_DATA,
                JSON.stringify(storedCustomers)
              )

              if (shouldRedirect) {
                route.push(appRoutes.tickets)
              }
            }
          }
        )
      )

      dispatch(
        setSelectedCustomersData(
          {
            storeCustomerId: findCustomer?.storeCustomerId,
            isSelectedCustomer: 1,
          },
          selectedCustomerID !== undefined ? Number(selectedCustomerID) : null
        )
      )
    },
    [customers, dispatch, route, shouldRedirect]
  )

  return { handleClickCustomer }
}
