/**
 * Epson Test Print Utility
 * Generates ESC/POS commands for test printing on Epson printers
 */

import { EpsonTestPrintData } from '@/types/module/printersModule'

// ESC/POS Command Constants
const ESC = '\x1B'
const GS = '\x1D'
const LF = '\x0A'
const CR = '\x0D'

// Text formatting commands
const COMMANDS = {
  INIT: ESC + '@',
  BOLD_ON: ESC + 'E' + '\x01',
  BOLD_OFF: ESC + 'E' + '\x00',
  UNDERLINE_ON: ESC + '-' + '\x01',
  UNDERLINE_OFF: ESC + '-' + '\x00',
  CENTER: ESC + 'a' + '\x01',
  LEFT: ESC + 'a' + '\x00',
  RIGHT: ESC + 'a' + '\x02',
  DOUBLE_HEIGHT: GS + '!' + '\x01',
  DOUBLE_WIDTH: GS + '!' + '\x10',
  DOUBLE_SIZE: GS + '!' + '\x11',
  NORMAL_SIZE: GS + '!' + '\x00',
  CUT_PAPER: GS + 'V' + '\x41' + '\x03',
  FEED_LINES: (lines: number) => ESC + 'd' + String.fromCharCode(lines),
  BARCODE_HEIGHT: (height: number) => GS + 'h' + String.fromCharCode(height),
  BARCODE_WIDTH: (width: number) => GS + 'w' + String.fromCharCode(width),
  BARCODE_CODE128: GS + 'k' + '\x49',
  QR_CODE_SIZE: (size: number) => GS + '(k' + '\x03\x00' + '1C' + String.fromCharCode(size),
  QR_CODE_ERROR: GS + '(k' + '\x03\x00' + '1E' + '0',
  QR_CODE_STORE: (data: string) => GS + '(k' + String.fromCharCode(data.length + 3, 0) + '1P0' + data,
  QR_CODE_PRINT: GS + '(k' + '\x03\x00' + '1Q0',
}

/**
 * Generate Epson ESC/POS test print data
 */
export function generateEpsonTestPrint(testData: EpsonTestPrintData): string {
  let output = ''

  // Initialize printer
  output += COMMANDS.INIT

  // Header
  output += COMMANDS.CENTER
  output += COMMANDS.DOUBLE_SIZE
  output += COMMANDS.BOLD_ON
  output += 'PRINTER TEST' + LF
  output += COMMANDS.NORMAL_SIZE
  output += COMMANDS.BOLD_OFF
  output += LF

  // Store information
  if (testData.storeName) {
    output += COMMANDS.CENTER
    output += COMMANDS.BOLD_ON
    output += testData.storeName + LF
    output += COMMANDS.BOLD_OFF
  }

  if (testData.storeAddress) {
    output += COMMANDS.CENTER
    output += testData.storeAddress + LF
  }

  output += LF

  // Test information
  output += COMMANDS.LEFT
  output += 'Test Date: ' + new Date().toLocaleString() + LF
  output += 'Test ID: ' + generateTestId() + LF
  output += LF

  // Printer capabilities test
  output += COMMANDS.BOLD_ON
  output += 'PRINTER CAPABILITIES TEST' + LF
  output += COMMANDS.BOLD_OFF
  output += '--------------------------------' + LF

  // Text formatting test
  output += 'Normal Text' + LF
  output += COMMANDS.BOLD_ON + 'Bold Text' + COMMANDS.BOLD_OFF + LF
  output += COMMANDS.UNDERLINE_ON + 'Underlined Text' + COMMANDS.UNDERLINE_OFF + LF
  output += COMMANDS.DOUBLE_HEIGHT + 'Double Height' + COMMANDS.NORMAL_SIZE + LF
  output += COMMANDS.DOUBLE_WIDTH + 'Double Width' + COMMANDS.NORMAL_SIZE + LF
  output += LF

  // Alignment test
  output += COMMANDS.LEFT + 'Left Aligned' + LF
  output += COMMANDS.CENTER + 'Center Aligned' + LF
  output += COMMANDS.RIGHT + 'Right Aligned' + LF
  output += COMMANDS.LEFT + LF

  // Character set test
  output += 'Character Test: áéíóú ñÑ ¿¡ €$£¥' + LF
  output += 'Numbers: 0123456789' + LF
  output += 'Symbols: !@#$%^&*()_+-=[]{}|;:,.<>?' + LF
  output += LF

  // Test pattern
  if (testData.testPattern) {
    output += COMMANDS.BOLD_ON
    output += 'TEST PATTERN' + LF
    output += COMMANDS.BOLD_OFF
    output += generateTestPattern() + LF
  }

  // Barcode test
  if (testData.includeBarcode) {
    output += COMMANDS.CENTER
    output += COMMANDS.BOLD_ON
    output += 'BARCODE TEST' + LF
    output += COMMANDS.BOLD_OFF
    output += generateBarcode('123456789012') + LF
  }

  // QR Code test
  if (testData.includeQRCode) {
    output += COMMANDS.CENTER
    output += COMMANDS.BOLD_ON
    output += 'QR CODE TEST' + LF
    output += COMMANDS.BOLD_OFF
    output += generateQRCode('https://washandbuzz.com/test') + LF
  }

  // Custom text
  if (testData.customText) {
    output += COMMANDS.LEFT
    output += COMMANDS.BOLD_ON
    output += 'CUSTOM TEXT' + LF
    output += COMMANDS.BOLD_OFF
    output += testData.customText + LF
    output += LF
  }

  // Footer
  output += '--------------------------------' + LF
  output += COMMANDS.CENTER
  output += 'Test completed successfully!' + LF
  output += 'If you can read this, your printer' + LF
  output += 'is working correctly.' + LF
  output += LF

  // Feed and cut
  output += COMMANDS.FEED_LINES(3)
  output += COMMANDS.CUT_PAPER

  return output
}

/**
 * Generate a simple test pattern
 */
function generateTestPattern(): string {
  let pattern = ''
  
  // Density test
  pattern += 'Density Test:' + LF
  pattern += '████████████████████████████████' + LF
  pattern += '▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓' + LF
  pattern += '▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒' + LF
  pattern += '░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░' + LF
  pattern += LF

  // Grid pattern
  pattern += 'Grid Pattern:' + LF
  for (let i = 0; i < 5; i++) {
    pattern += '┌─┬─┬─┬─┬─┬─┬─┬─┬─┬─┬─┬─┬─┬─┬─┐' + LF
    pattern += '│ │ │ │ │ │ │ │ │ │ │ │ │ │ │ │' + LF
  }
  pattern += '└─┴─┴─┴─┴─┴─┴─┴─┴─┴─┴─┴─┴─┴─┴─┘' + LF

  return pattern
}

/**
 * Generate barcode (Code 128)
 */
function generateBarcode(data: string): string {
  let barcode = ''
  
  // Set barcode parameters
  barcode += COMMANDS.BARCODE_HEIGHT(50)
  barcode += COMMANDS.BARCODE_WIDTH(2)
  
  // Print barcode
  barcode += COMMANDS.BARCODE_CODE128
  barcode += String.fromCharCode(data.length)
  barcode += data
  
  return barcode
}

/**
 * Generate QR Code
 */
function generateQRCode(data: string): string {
  let qrCode = ''
  
  // Set QR code parameters
  qrCode += COMMANDS.QR_CODE_SIZE(6) // Size 6
  qrCode += COMMANDS.QR_CODE_ERROR // Error correction level L
  
  // Store data
  qrCode += COMMANDS.QR_CODE_STORE(data)
  
  // Print QR code
  qrCode += COMMANDS.QR_CODE_PRINT
  
  return qrCode
}

/**
 * Generate unique test ID
 */
function generateTestId(): string {
  const timestamp = Date.now().toString(36)
  const random = Math.random().toString(36).substr(2, 5)
  return `TEST-${timestamp}-${random}`.toUpperCase()
}

/**
 * Convert string to base64 for transmission
 */
export function encodeTestPrintData(data: string): string {
  // Convert string to bytes
  const bytes = new TextEncoder().encode(data)
  
  // Convert to base64
  let binary = ''
  bytes.forEach(byte => {
    binary += String.fromCharCode(byte)
  })
  
  return btoa(binary)
}

/**
 * Create a simple receipt-style test print
 */
export function generateSimpleTestReceipt(storeName?: string): string {
  let output = ''

  // Initialize
  output += COMMANDS.INIT

  // Header
  output += COMMANDS.CENTER
  output += COMMANDS.DOUBLE_SIZE
  output += COMMANDS.BOLD_ON
  output += (storeName || 'TEST STORE') + LF
  output += COMMANDS.NORMAL_SIZE
  output += COMMANDS.BOLD_OFF
  output += LF

  // Receipt details
  output += COMMANDS.LEFT
  output += 'Receipt #: TEST-001' + LF
  output += 'Date: ' + new Date().toLocaleDateString() + LF
  output += 'Time: ' + new Date().toLocaleTimeString() + LF
  output += LF

  // Items
  output += '--------------------------------' + LF
  output += 'Test Item 1              $10.00' + LF
  output += 'Test Item 2               $5.50' + LF
  output += '--------------------------------' + LF
  output += COMMANDS.BOLD_ON
  output += 'TOTAL:                   $15.50' + LF
  output += COMMANDS.BOLD_OFF
  output += '--------------------------------' + LF
  output += LF

  // Footer
  output += COMMANDS.CENTER
  output += 'Thank you for testing!' + LF
  output += 'Printer is working correctly.' + LF
  output += LF

  // Cut
  output += COMMANDS.FEED_LINES(3)
  output += COMMANDS.CUT_PAPER

  return output
}

/**
 * Generate minimal test print for quick testing
 */
export function generateMinimalTestPrint(): string {
  let output = ''
  
  output += COMMANDS.INIT
  output += COMMANDS.CENTER
  output += COMMANDS.BOLD_ON
  output += 'PRINTER TEST' + LF
  output += COMMANDS.BOLD_OFF
  output += new Date().toLocaleString() + LF
  output += LF
  output += 'If you can read this,' + LF
  output += 'your printer is working!' + LF
  output += LF
  output += COMMANDS.FEED_LINES(2)
  output += COMMANDS.CUT_PAPER
  
  return output
}
