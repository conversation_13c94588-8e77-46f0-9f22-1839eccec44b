import { FulfillmentMethodLabel } from '@/components/Fulfillment/FulfillmentMethodButton'
import { STMConfigType } from '@/types/module/ticketNdSalesModule'
import { getFormattedDueDate } from '@/utils/date'

export const quickDropoffCopy = async (data: Partial<STMConfigType>) => {
  const {
    store,
    adjustedDate,
    ticketNumber,
    customer,
    fulfillmentDetail,
    fulfillmentDay,
    notes,
    ticketMessage,
    isMessageOnTicketEnabled,
    isStoreInfoEnabled,
    isTicketBarcodeEnabled,
    storeData,
    isCustomerCopy,
    isCustomerAddressEnabled,
  } = data

  const includeAddress = isCustomerAddressEnabled && customer?.address
  let stmContent = ``

  // Rush (If Ticket is not RUSH, do not print this)
  if (fulfillmentDetail?.rush && !isCustomerCopy) {
    stmContent += `[plain]\
                [align: center]\
                [bold: on]\
                [magnify: width 6; height 6]\
                RUSH
                `
  }
  // Store Info based on setting
  if (isStoreInfoEnabled) {
    stmContent += `[plain]\
                  [align: center]\
                  [bold: on]\
                  [magnify: width 2; height 2]\
                  ${store?.name}
                  [plain]\
                  ${store?.address}
                  ${store?.city} ${store?.state} ${store?.zipCode}
                  ${store?.phoneNumber}
                  `
  }
  // Ticket Created Clerk/Time for Customer Copy & Store Copy
  stmContent += `[plain]\
                [align: center]\
                [magnify: width 1; height 1]
                Clerk: ${storeData?.teamMemberName}\
                [space: count 3]\
                Created: ${adjustedDate}\
                `
  // Line Separator
  stmContent += `[plain]\
                [align: center]\
                ········································`
  // Ticket #
  stmContent += `[plain]\
                [align: center]\
                [bold: on]\
                [magnify: width 5; height 4]\
                ${ticketNumber}
                `
  // Barcode based on settings
  if (isTicketBarcodeEnabled) {
    stmContent += `[barcode: type code39; data ${ticketNumber}; height 6mm;
              module 3]\
              `
  }

  // Customer Name with Fulfillment Method
  stmContent += `[plain]\
              [magnify: width 2; height 1]\
              [bold: on]\
              [column: left ${customer?.name}; right ${(fulfillmentDetail?.method && FulfillmentMethodLabel[fulfillmentDetail?.method as keyof typeof FulfillmentMethodLabel]) || ''}; indent 0]
              `

  // Fulfillment Date
  stmContent += `[plain]\
                 [column: ${isCustomerCopy ? '.' : includeAddress}; right ${
                   (fulfillmentDetail &&
                     fulfillmentDetail.date &&
                     fulfillmentDetail.time &&
                     getFormattedDueDate(
                       fulfillmentDetail.date,
                       fulfillmentDetail.time
                     )) ||
                   ''
                 }]
                `

  // Note based on settings
  if (notes) {
    stmContent += `[plain]\
                   [bold: on]\
                   [magnify: width 2; height 2]\
                   [column: left ${notes} items; right ${fulfillmentDay?.slice(0, 3) || ''}]
                  `
  }

  // Ticket Summary
  stmContent += `[plain]\
                 [align: center]\
                 ______________________________________________
                 [column: left Subtotal; right -; indent 250]
                 [column: left Tax; right -]
                 [magnify: width 2; height 1]\
                 [bold: on]\
                 [column: left Total; right -]
                `

  // Message (based on settings)
  if (isMessageOnTicketEnabled) {
    stmContent += `
                [plain]\
                ${ticketMessage}\
                `
  }

  // Ticket Type
  stmContent += `
                [plain]\
                [bold: on]\
                [magnify: width 2; height 2]
                ${isCustomerCopy ? 'Customer Copy' : 'Store Copy'}
                `

  // Cut
  stmContent += `
  [feed: length 0]\
  [cut: feed; partial]
  `
  return stmContent
}
