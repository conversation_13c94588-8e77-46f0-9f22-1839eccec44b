import { FulfillmentTabs } from '@/types/module/fulfillmentModule'
import {
  CleaningService,
  CleaningServiceForBackendPayload,
} from '@/types/module/itemModule'
import { CleaningServices } from '@/types/module/itemPriceListModule'
import { appRoutes } from '@/utils/routes'
import { translation } from '@/utils/translation'

export const PRINT_DATA =
  'G0AbUgAbHkYAGyAAG2wAG3oBGx0pVQIAMAEbHSlVAgBAABsdYQEKG2kBAQpXQiBEcnkgQ2xlYW5lcnMKEhtpAAAbaQAAGx5GABstABtGG18AGzUyMTUtMDQgMzl0aCBBdmUKQmF5c2lkZSBOWSAxMTM2MQo5MTcgNjkzLTA0OTIKChtpAAAKUGxhY2VkIGF0IE1hcmNoIDI0IDIwMjEgMTozMFBNChIbaQAAG2kAABseRgAbLQAbRhtfABs1wrfCt8K3wrfCt8K3wrfCt8K3wrfCt8K3wrfCt8K3wrfCt8K3wrfCt8K3wrfCt8K3wrfCt8K3wrfCt8K3wrfCt8K3wrfCt8K3wrfCt8K3wrcKEhtpAAAbaQAAGx5GABstABtGG18AGzUKG0UbaQICClJVU0gSG2kAABtpAAAbHkYAGy0AG0YbXwAbNQobRRtpAgIwMzktMTc3ChtiBAEHSDEyMzQ1Njc4OTAxMh4KEhtpAAAbaQAAGx5GABstABtGG18AGzUbaQAAChsdQQAASXRlbSBDb3VudBsdQe+/vQFQaWNrdXAKG0UbaQICChsdQQAAMyBwY3MbHUHvv70BVEhVChIbaQAAG2kAABseRgAbLQAbRhtfABs1Gx1BAAAzIGl0bXMbHUFEATMvMjcvMjQgQWZ0ZXIgMTI6MDBQTQoKwrfCt8K3wrfCt8K3wrfCt8K3wrfCt8K3wrfCt8K3wrfCt8K3wrfCt8K3wrfCt8K3wrfCt8K3wrfCt8K3wrfCt8K3wrfCt8K3wrfCt8K3wrcKG2kBAQobRRsdYQAKU2FtIFJpbSAjMTIzNBIbaQAAG2kAABseRgAbLQAbRhtfABs1ChsdQQAAMjE1LTA0IDM5dGggQXZlGx1B77+9AUluZGl2aWR1YWwKGx1BAAAjMTdBGx1B77+9ASg5MTcpIDY5My0wNDkyChsdYQAKTWVtbzogTG9yZW0gSXBzdW1vYWtzZGZhc2Rmb2sKGx1hAQrCt8K3wrfCt8K3wrfCt8K3wrfCt8K3wrfCt8K3wrfCt8K3wrfCt8K3wrfCt8K3wrfCt8K3wrfCt8K3wrfCt8K3wrfCt8K3wrfCt8K3wrfCtwoSG2kAABtpAAAbHkYAGy0AG0YbXwAbNQobRRtpAQAKGx1BAAAxIENoaXBzGx1BEAIzLjAwChsdQQAAMyBCdXR0b24gRG93bhsdQQQCMTUuMDAKEhtpAAAbaQAAGx5GABstABtGG18AGzUbaQEAChsdQTIAMiBSZWd1bGFyChsdQTIAMSBUZWFyLCBMZWZ0LCBUZWFyGx1BBAIxMC4wMAoSG2kAABtpAAAbHkYAGy0AG0YbXwAbNQobHWEACk5vdGU6IExvcmVtIElwc3VtIGFsc2tkZmphc2RrZmobHWECCl9fCgobHUEAAFN1YnRvdGFsGx1BBAI1NS45NQobHUEAAE1lbW8bHUEQAjEuMDAKGx1BAABUYXgbHUEQAjUuNjAbaQEBChtFGx1BAABUb3RhbBsdQe+/vQE2Mi41NQoSG2kAABtpAAAbHkYAGy0AG0YbXwAbNRsdQQAAQ2FzaBsdQQQCNjUuMDAKGx1BAABDaGFuZ2UbHUEQAjMuNTUKGx1hAQobRQpSZWxlYXNlIG9mIFJlc3BvbnNpYmlsaXR5ChIbaQAAG2kAABseRgAbLQAbRhtfABs1CkxvcmVtIElwc29tCgrCt8K3wrfCt8K3wrfCt8K3wrfCt8K3wrfCt8K3wrfCt8K3wrfCt8K3wrfCt8K3wrfCt8K3wrfCt8K3wrfCt8K3wrfCt8K3wrfCt8K3wrfCtwpUaGFuayBZb3UhCgobZAM='
// cspell:disable-next-line
export const MEDIA_TYPE = 'application/vnd.star.starprnt'
export const CLIENT_ID = '1-abc123'
export const JOB_TOKEN = 'tokenID_100'
export const JOB_TYPE = 'raw'
export const VARIANT = 'VARIANT'
export const DELETE = 'DELETE'
export const ADD = 'ADD'
export const TICKET_POS = 'TICKET_POS'
export const POS_TICKET = 'POS_TICKET'
export const IDB_VERSION = 43
export const TICKET = 'TICKET'
export const EXTERNAL = 'EXTERNAL'
export const SALES = 'Sales'
export const ADMIN_CHANGE_STORE = 'ADMIN_CHANGE_STORE'
export const ADMIN_ACCESS_SETTINGS = 'ADMIN_ACCESS_SETTINGS'
export const REDUX_STATE_UPDATED = 'reduxStateUpdated'
export const IS_PIN_MODAL_OPEN = 'isPinModalOpen'
export const HAS_PERMISSION = 'hasPermission'
export const FAILED_PERMISSIONS = 'failedPermission'
export const POS_PRICE_ADJUSTMENT_CUSTOM_DISCOUNT =
  'POS_PRICE_ADJUSTMENT_CUSTOM_DISCOUNT'
export const POS_PRICE_ADJUSTMENT_COMP = 'POS_PRICE_ADJUSTMENT_COMP'
export const POS_PRICE_ADJUSTMENT_DEBIT_MEMO = 'POS_PRICE_ADJUSTMENT_DEBIT_MEMO'
export const POS_PRICE_ADJUSTMENT_CREDIT_MEMO =
  'POS_PRICE_ADJUSTMENT_CREDIT_MEMO'
export const CUSTOMER_REIMBURSEMENT_REFUND = 'CUSTOMER_REIMBURSEMENT_REFUND'
// cspell:disable
export const STATIC_TOPIC_FOR_SUBSCRIBE =
  'star/cloudprnt/to-server/00:11:62:41:cb:a9/client-status' // FIXME Will come dynamically
export const STATIC_TOPIC_FOR_PUBLISH =
  'star/cloudprnt/to-device/00:11:62:41:cb:a9/request-client-status' // FIXME Will come dynamically----
export const STATIC_TOPIC_FOR_PRINT_PUBLISH =
  'star/cloudprnt/to-device/00:11:62:41:cb:a9/print-job' // FIXME Will come dynamically
export const ALL_PRODUCTS = [1, 2, 3]
export const TICKET_ID = 'ticketId'

export const PERMISSION_MODULE_DROPDOWN = [
  {
    value: 'Owner',
    label: 'Owner',
  },
  {
    value: 'Clerk',
    label: 'Clerk',
  },
  {
    value: 'Manager',
    label: 'Manager',
  },
  {
    value: 'Driver',
    label: 'Driver',
  },
]
export const POS_PRODUCT_TYPE = 'pos-product-type'
export const EMAIL = 'email'
export const IS_STORE_DATA_SELECTED = 'is-store-data-selected'
export const IS_TEAM_MEMBER_SELECTED = 'is-team-member-selected'
export const HAS_SETTING_PERMISSION = 'has-setting-permission'
export const FALSE = 'false'
export const TRUE = 'true'
export const ACCESS_TOKEN = 'accessToken'
export const TOKEN_SET_TIMEOUT = 'tokenSetTimeout'
export const AR_BILLING_CONTINUATION_TOKEN = 'arBillingContinuationToken'
export const SELECTED_CUSTOMER = 'selected-customer'
export const SELECTED_CUSTOMER_SEARCH_DATA = 'SelectedCustomersSearchData'
export const PAYMENT_TERMINAL_READ_CARD = '/payment/terminals/read-card'
export const PAYMENT_TERMINAL_SEND_CANCEL = '/payment/terminals/send-cancel'
export const REVOKE_PATH = '/auth/revoke'
export const TRANSACTION_ERROR =
  'Payment process was interrupted. Please initiate the transaction again.'
export const COMMON_TOAST_TIMEOUT = 3000
export const COMMON_API_TIMEOUT = 60000
export const PAYMENT_TERMINAL_API_TIMEOUT = 120000
export const FLAG = 'FLAG'
export const REFUND = 'REFUND'
export const UNCLAIMED = 'UNCLAIMED'
export const VOID = 'VOID'
export const TOAST_COMMON_POSITION = 'bottom-center'
export const TOAST_COMMON_THEME = 'colored'
export const TOAST_CLOSE_ICON_ALT = 'toast-close-icon'
export const TOAST_ERROR_ICON_ALT = 'toast-error-icon'
export const UNIQUE_INSTANCES = 'uniqueInstances'
export const SESSION_ID = 'sessionId'
export const SINGLETON_CHANNEL = 'singletonChannel'
export const ACTIVE_SESSION_CHANGE = 'activeSessionChange'
export const IS_RELOADING = 'isReloading'
export const GROUP_POS = 'groupPos'
export const All = 'All'
export const ITEMS = 'items'
export const TEXT = 'text'
export const WHITE_COLOR_CODE = '#FFFFFF'
export const TAB_COMMUNICATION = 'Tab comunication'
export const UP = 'up'
export const DOWN = 'down'
export const LEFT = 'left'
export const RIGHT = 'right'
export const ARROW_DOWN = 'ArrowDown'
export const ARROW_UP = 'ArrowUp'
export const TAB = 'Tab'
export const DATE_FORMAT_YYYY_MM_DD = 'yyyy-MM-dd'
export const DATE_FORMAT_MMMM_DD_YYYY = 'MMMM dd, yyyy'
export const DATE_FORMAT_HHMMA = 'hhmma'
export const DATE_FORMAT_MM_DD_YY_HH_MM_A_WITHOUT_COMMA = 'MM/dd/yy hh:mm a'
export const DATE_FORMAT_MM_DD_YY_HH_MM_A_WITH_AT = 'MM/dd/yy @ hh:mm a'
export const DATE_FORMAT_MM_DD_YY_HH_MM_A_WITH_TIME_LABEL =
  'MMM dd, yyyy · hh:mm a'
export const DATE_FORMAT_YYYY_MM_DD_HH_MM = 'yyyy-MM-dd HH:mm'
export const DATE_FORMAT_MM_DD_YY_HH_MM_A = 'MM/dd/yy, hh:mm a'
export const DATE_FORMAT_MM_DD_YY = 'MM/dd/yy'
export const TICKET_DATE_FORMAT = 'yyyy-MM-dd'
export const SAVE_BUTTON_ID = 'save-button'
export const NEXT_BUTTON_ID = 'next-button'

export const A4SizePage = 'A4'

export const guestMenuDataForP2 = [
  appRoutes.tickets,
  appRoutes.arBilling,
  appRoutes.delivery,
]
export const guestMenuDataForP3 = [...guestMenuDataForP2, appRoutes.assignRack]

// Dynamic mapping configuration
export const titleMapping: Record<string, string> = {
  POS_PRICE_ADJUSTMENT: 'Edit price adjustment in POS',
  CUSTOMER_REIMBURSEMENT: 'Authorize customer reimbursement',
  ADMIN: 'Admin settings',
  // Add more mappings here if needed
}

export const subDataMapping: Record<string, string> = {
  CHANGE_STORE: 'Change store',
  ACCESS_SETTINGS: 'Open settings',
  CUSTOM_DISCOUNT: 'Create custom discount',
  COMP: 'Access to comp ticket',
  DEBIT_MEMO: 'Create debit memo',
  CREDIT_MEMO: 'Create credit memo',
  NEGATIVE_SUBTOTAL: 'Authorize customer credit or cash for negative subtotals',
  REFUND: 'Refund tickets',
  // Add more mappings here if needed
}

// associates with enum TicketFilters
const TicketStatusFiltersValue = {
  OPEN: 'OPEN',
  CLOSED: 'CLOSED',
  FLAG: 'FLAG',
  VOID: 'VOID',
  UNCLAIMED: 'UNCLAIMED',
} as const

// associates with enum TicketPaymentFilters
const TicketPaymentFiltersValue = {
  PAID: 'PAID',
  UNPAID: 'UNPAID',
  PARTIAL: 'PARTIAL',
} as const

export const ticketFiltersValues = [
  {
    label: translation.OPEN,
    value: TicketStatusFiltersValue.OPEN,
  },
  {
    label: translation.CLOSED,
    value: TicketStatusFiltersValue.CLOSED,
  },
  {
    label: translation.FLAG,
    value: TicketStatusFiltersValue.FLAG,
  },
  {
    label: translation.VOID,
    value: TicketStatusFiltersValue.VOID,
  },
  {
    label: translation.UNCLAIMED,
    value: TicketStatusFiltersValue.UNCLAIMED,
  },
]

export const paymentStatusOptions = [
  {
    label: translation.PAID,
    value: TicketPaymentFiltersValue.PAID,
  },
  {
    label: translation.UNPAID,
    value: TicketPaymentFiltersValue.UNPAID,
  },
  {
    label: translation.PARTIAL,
    value: TicketPaymentFiltersValue.PARTIAL,
  },
]

export const dateRangeOptions = [
  { label: translation?.LAST_7_DAYS, value: translation?.LAST_7_DAYS_VALUE },
  { label: translation?.LAST_30_DAYS, value: translation?.LAST_30_DAYS_VALUE },
  { label: translation?.LAST_90_DAYS, value: translation?.LAST_90_DAYS_VALUE },
  {
    label: translation?.LAST_12_MONTHS,
    value: translation?.LAST_12_MONTHS_VALUE,
  },
  { label: translation?.CUSTOM, value: translation.CUSTOM_FILTER },
]

export const uniqueStoreId = 1

export const keyPadButtons = [
  '1',
  '2',
  '3',
  '4',
  '5',
  '6',
  '7',
  '8',
  '9',
  '0',
  '.',
  '/images/clear.svg',
]
export const quickDropoffKeyPadButtons = [
  '1',
  '2',
  '3',
  '4',
  '5',
  '6',
  '7',
  '8',
  '9',
  '0',
  '/images/clear.svg',
]

export const timeZones = [
  {
    city: '(GMT-08:00/-07:00) Pacific Time - Los Angeles',
    timezone: 'America/Los_Angeles',
  },
  {
    city: '(GMT-08:00/-07:00) Pacific Time - Seattle',
    timezone: 'America/Los_Angeles',
  },
  {
    city: '(GMT-07:00) Mountain Time - Phoenix',
    timezone: 'America/Phoenix',
  },
  {
    city: '(GMT-07:00/-06:00) Mountain Time - Denver',
    timezone: 'America/Denver',
  },
  {
    city: '(GMT-06:00/-05:00) Central Time - Chicago',
    timezone: 'America/Chicago',
  },
  {
    city: '(GMT-05:00/-04:00) Eastern Time - New York',
    timezone: 'America/New_York',
  },
  {
    city: '(GMT-05:00/-04:00) Eastern Time - Boston',
    timezone: 'America/New_York',
  },
  {
    city: '(GMT-05:00/-04:00) Eastern Time - Detroit',
    timezone: 'America/Detroit',
  },
  {
    city: '(GMT-05:00/-04:00) Eastern Time - Atlanta',
    timezone: 'America/New_York',
  },
]

export const authAndSelectionRoutes = [
  appRoutes.signIn,
  appRoutes.selectStoreData,
  appRoutes.selectPrinterData,
]

export const terminalAndOtherSelectionRoutes = [
  appRoutes.signIn,
  appRoutes.selectStoreData,
  appRoutes.selectPrinterData,
  appRoutes.selectPaymentTerminalData,
]

export const iframeCss = `
  body{
  margin: 0;
  }
  #cccardlabel,#ccexpirylabel,#cccvvlabel {
  font-family: "Poppins", system-ui;
  font-weight: 500;
  line-height: 125%;
  font-size: 16px;
  color: #303030;
  margin-bottom: 5px;
  display: inline-block;
  }
  #ccnumfield{
  width: -webkit-fill-available
  }

  #ccnumfield,#ccexpirymonth,#ccexpiryyear,#cccvvfield {
  border-radius: 6px;
  border: 2px solid #eaeaea;
  padding: 14px;
  color: #303030;
  outline-color: #00c6b7;
  font-family: 'Noto Sans', sans-serif;
  font-size: 14px;
  line-height: 125%;
  font-weight: 500;
  box-shadow: 0px 0px 3px 0px rgba(0, 0, 0, 0.2);
  background-color: transparent;
  margin-bottom: 20px;
  }
  #ccnumfield.error{
  color : red;
  border-color : red !important;
  display: inline-block !important;
  }
  `
export const quantityModalCarouselData: number[] = [
  1, 2, 3, 4, 5, 6, 7, 8, 9, 0,
]

export const routeGroups = {
  groupPos: [
    appRoutes.dashboard,
    appRoutes.pos,
    appRoutes.tickets,
    appRoutes.arBilling,
    appRoutes.delivery,
    appRoutes.profile,
    appRoutes.assignRack,
    appRoutes.defaultState,
  ],
  groupSettings: [
    appRoutes.myTeam,
    appRoutes.operations,
    appRoutes.generalDisplay,
    appRoutes.fulfillment,
    appRoutes.priceAdjustment,
    appRoutes.tax,
    appRoutes.ticketSettings,
    appRoutes.reports,
    appRoutes.storeDetails,
    appRoutes.categories,
    appRoutes.itemsPriceList,
    appRoutes.variants,
    appRoutes.permissions,
    appRoutes.positions,
    appRoutes.teamMembers,
    appRoutes.hardware,
    appRoutes.printer,
    appRoutes.printerDiscovery,
    appRoutes.paymentTerminal,
    appRoutes.revenueSummary,
    appRoutes.salesAnalysis,
    appRoutes.itemTransactions,
  ],
}

export const fulfillmentTabs: FulfillmentTabs[] = [
  { tabName: translation.GENERAL_TAB, displayName: translation.GENERAL },
  {
    tabName: translation.STORE_PICKUP_TAB,
    displayName: translation.STORE_PICKUP,
  },
  {
    tabName: translation.PICKUP_DELIVERY_TAB,
    displayName: translation.PICKUP_DELIVERY,
  },
]

export const timeOptions = [translation.AM, translation.PM]

export const cleaningWeightedColumnData = [
  {
    value: translation?.BASE_PRICE,
  },
  {
    value: translation?.UP_TO_LBS,
  },
  {
    value: translation?.ADDITIONAL_PER_LB,
  },
]
export const cleaningItemColumnData = [
  {
    value: CleaningService?.DRY_CLEAN,
  },
  {
    value: CleaningService?.LAUNDRY,
  },
  {
    value: CleaningService?.PRESS_ONLY,
  },
  {
    value: CleaningService?.WET_CLEAN,
  },
]

export const patternBigImageData = {
  PATTERN_ANIMAL: '/images/pattern-animal-big.svg',
  PATTERN_CHECKER: '/images/pattern-checker-big.svg',
  PATTERN_DOTS: '/images/pattern-dots-big.svg',
  PATTERN_SOLID: '/images/pattern-solid-big.svg',
  PATTERN_STRIPE: '/images/pattern-stripe-big.svg',
  PATTERN_PLAID: '/images/pattern-plaid-big.svg',
  PATTERN_FLORAL: '/images/pattern-floral-big.svg',
}

export const patternMediumImageData = {
  PATTERN_ANIMAL: '/images/pattern-animal-medium.svg',
  PATTERN_CHECKER: '/images/pattern-checker-medium.svg',
  PATTERN_DOTS: '/images/pattern-dots-medium.svg',
  PATTERN_SOLID: '/images/pattern-solid-medium.svg',
  PATTERN_STRIPE: '/images/pattern-stripe-medium.svg',
  PATTERN_PLAID: '/images/pattern-plaid-medium.svg',
  PATTERN_FLORAL: '/images/pattern-floral-medium.svg',
}

export const colorData = {
  COLOR_BLACK: '#000000',
  COLOR_GREY: '#D8D8D8',
  COLOR_WHITE: '#FFFFFF',
  COLOR_BEIGE: '#EAD8C6',
  COLOR_BROWN: '#826447',
  COLOR_PURPLE: '#B042FF',
  COLOR_PINK: '#FFAFF2',
  COLOR_RED: '#F90C0C',
  COLOR_ORANGE: '#F49507',
  COLOR_YELLOW: '#ECE32C',
  COLOR_GREEN: '#44FC40',
  COLOR_BLUE: '#0000FF',
  COLOR_NAVYBLUE: '#000080',
  COLOR_LIGHTBLUE: '#97BAFF',
}

export const fabricAddons = {
  ...patternMediumImageData,
  ...colorData,
}

export const cleaningService = [
  CleaningService.DRY_CLEAN,
  CleaningService.WET_CLEAN,
  CleaningService.PRESS_ONLY,
  CleaningService.LAUNDRY,
  CleaningService.DO_NOT_CLEAN,
  CleaningService.DO_OVER,
  CleaningService.NO_CHARGE,
]
export const p1ItemTypeTabs = [
  {
    label: translation?.SALES,
    key: translation?.SALES,
  },
  {
    label: translation?.OTHER,
    key: translation?.OTHER,
  },
]

export const p2ItemTypeTabs = [
  {
    label: translation?.CLEANING_WEIGHT,
    key: translation?.WEIGHTED?.toLocaleLowerCase(),
  },
  {
    label: translation?.MAID,
    key: translation?.MAID,
  },
  {
    label: translation?.SALES,
    key: translation?.SALES,
  },
  {
    label: translation?.OTHER,
    key: translation?.OTHER,
  },
]

export const p3ItemTypeTabs = [
  {
    label: translation?.CLEANING,
    key: translation?.CLEANING,
  },
  {
    label: translation?.CLEANING_WEIGHT,
    key: translation?.WEIGHTED?.toLowerCase(),
  },
  {
    label: translation?.MAID,
    key: translation?.MAID,
  },
  {
    label: translation?.SALES,
    key: translation?.SALES,
  },
  {
    label: translation?.OTHER,
    key: translation?.OTHER,
  },
]

export const serviceType = [
  CleaningServices.DRY_CLEAN,
  CleaningServices.WET_CLEAN,
  CleaningServices.PRESS_ONLY,
  CleaningServices.LAUNDRY,
]

export const p2AndP3ItemTypes = [
  translation?.WEIGHTED?.toLowerCase(),
  translation?.MAID?.toLowerCase(),
  translation?.CLEANING?.toLowerCase(),
]

export const p2AndP3Item = [
  translation?.WEIGHTED?.toLocaleUpperCase(),
  translation?.MAID?.toLocaleUpperCase(),
  translation?.CLEANING?.toLocaleUpperCase(),
]

// TODO: Temporary Data for time slot
// TODO: When we get data from API, remove this temporary data

export const deliveryTimeRangeSlots = [
  '9:00 AM - 10:00 AM',
  '12:00 PM - 1:00 PM',
  '3:00 PM - 4:00 PM',
  '6:00 PM - 7:00 PM',
]

export const noneChargeableServices = [
  CleaningServiceForBackendPayload.DO_NOT_CLEAN,
  CleaningServiceForBackendPayload.DO_OVER,
  CleaningServiceForBackendPayload.NO_CHARGE,
]

export const VOID_TICKET_OPTIONS: string[] = [
  'Issued by Mistake',
  'Withdrawal by Customer',
  'Not Completed',
  'Issued to Wrong Customer',
  'Duplicate of Ticket #',
  'Replaced by Ticket #',
]

export const ASSIGN_RACK_CONTINUATION_TOKEN = 'assignRackContinuationToken'
export const INVALID_TICKET_ERROR = 'Invalid ticket ID'
export const INVALID_RACK_ERROR = 'Invalid rack, currently occupied by tickets'
export const INVALID_TICKET_STATUS_ERROR = 'Invalid ticket status for tickets'
export const defaultStartDate = new Date(2000, 0)
