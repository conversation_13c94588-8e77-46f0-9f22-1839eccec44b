/**
 * USB Device Discovery Test Utility
 * Use this to test USB device discovery in the browser console
 */

export class USBDeviceTest {
  /**
   * Test if WebUSB is supported
   */
  static isWebUSBSupported(): boolean {
    return typeof navigator !== 'undefined' && 'usb' in navigator
  }

  /**
   * Get all paired USB devices
   */
  static async getPairedDevices(): Promise<USBDevice[]> {
    if (!this.isWebUSBSupported()) {
      throw new Error('WebUSB not supported')
    }

    try {
      const devices = await navigator.usb.getDevices()
      console.log(`Found ${devices.length} paired USB devices:`)
      devices.forEach((device, index) => {
        console.log(`${index + 1}. ${device.manufacturerName || 'Unknown'} ${device.productName || 'Unknown Device'}`)
        console.log(`   Vendor ID: 0x${device.vendorId.toString(16).padStart(4, '0')}`)
        console.log(`   Product ID: 0x${device.productId.toString(16).padStart(4, '0')}`)
        console.log(`   Serial: ${device.serialNumber || 'N/A'}`)
      })
      return devices
    } catch (error) {
      console.error('Error getting paired devices:', error)
      throw error
    }
  }

  /**
   * Request access to a USB device (opens browser picker)
   */
  static async requestDevice(options?: {
    epsonOnly?: boolean
    showAllDevices?: boolean
  }): Promise<USBDevice | null> {
    if (!this.isWebUSBSupported()) {
      throw new Error('WebUSB not supported')
    }

    const { epsonOnly = false, showAllDevices = true } = options || {}

    try {
      let filters = []

      if (epsonOnly) {
        filters = [{ vendorId: 0x04b8 }] // Epson only
      } else if (showAllDevices) {
        // Comprehensive filters to show all device types
        filters = [
          // Printers
          { vendorId: 0x04b8 }, // Epson
          { vendorId: 0x03f0 }, // HP
          { vendorId: 0x04a9 }, // Canon
          { vendorId: 0x04f9 }, // Brother
          
          // Input devices
          { vendorId: 0x046d }, // Logitech
          { vendorId: 0x1532 }, // Razer
          { vendorId: 0x045e }, // Microsoft
          
          // Storage
          { vendorId: 0x0781 }, // SanDisk
          { vendorId: 0x058f }, // Alcor Micro
          
          // Audio
          { vendorId: 0x0b05 }, // ASUS
          
          // Network
          { vendorId: 0x0bda }, // Realtek
          { vendorId: 0x2357 }, // TP-Link
          
          // Device classes
          { classCode: 3 }, // HID devices (keyboards, mice)
          { classCode: 8 }, // Mass storage
          { classCode: 7 }, // Printer class
          { classCode: 1 }, // Audio class
          { classCode: 2 }, // Communication class
          
          // Fallback - any device
          {}
        ]
      }

      console.log('Requesting USB device access...')
      console.log('This will open a browser dialog showing available USB devices.')
      
      const device = await navigator.usb.requestDevice({
        filters: filters
      })

      if (device) {
        console.log('Selected device:')
        console.log(`Name: ${device.manufacturerName || 'Unknown'} ${device.productName || 'Unknown Device'}`)
        console.log(`Vendor ID: 0x${device.vendorId.toString(16).padStart(4, '0')}`)
        console.log(`Product ID: 0x${device.productId.toString(16).padStart(4, '0')}`)
        console.log(`Serial: ${device.serialNumber || 'N/A'}`)
        
        return device
      }

      return null
    } catch (error) {
      if (error instanceof Error && error.name === 'NotFoundError') {
        console.log('User cancelled device selection or no devices available')
        return null
      }
      console.error('Error requesting USB device:', error)
      throw error
    }
  }

  /**
   * Test the complete USB discovery flow
   */
  static async testDiscovery(): Promise<void> {
    console.log('=== USB Device Discovery Test ===')
    
    // Check WebUSB support
    console.log('1. Checking WebUSB support...')
    if (!this.isWebUSBSupported()) {
      console.error('❌ WebUSB not supported in this browser')
      console.log('💡 Try using Chrome or Edge with HTTPS')
      return
    }
    console.log('✅ WebUSB is supported')

    // Get paired devices
    console.log('\n2. Getting already paired devices...')
    try {
      const pairedDevices = await this.getPairedDevices()
      if (pairedDevices.length === 0) {
        console.log('No devices are currently paired')
      }
    } catch (error) {
      console.error('Error getting paired devices:', error)
    }

    // Request new device
    console.log('\n3. Requesting device access...')
    console.log('This will open a browser dialog. Select your USB mouse or any other device.')
    
    try {
      const device = await this.requestDevice({ showAllDevices: true })
      if (device) {
        console.log('✅ Successfully selected device!')
        
        // Try to open the device
        console.log('\n4. Testing device connection...')
        try {
          await device.open()
          console.log('✅ Device opened successfully')
          
          // Get device info
          console.log('\nDevice Details:')
          console.log(`- Manufacturer: ${device.manufacturerName || 'Unknown'}`)
          console.log(`- Product: ${device.productName || 'Unknown'}`)
          console.log(`- Vendor ID: 0x${device.vendorId.toString(16).padStart(4, '0')}`)
          console.log(`- Product ID: 0x${device.productId.toString(16).padStart(4, '0')}`)
          console.log(`- Serial Number: ${device.serialNumber || 'N/A'}`)
          console.log(`- USB Version: ${device.deviceVersionMajor}.${device.deviceVersionMinor}.${device.deviceVersionSubminor}`)
          
          await device.close()
          console.log('✅ Device closed successfully')
        } catch (error) {
          console.warn('⚠️ Could not open device (this is normal for some devices):', error)
        }
      } else {
        console.log('No device selected')
      }
    } catch (error) {
      console.error('Error during device request:', error)
    }

    console.log('\n=== Test Complete ===')
  }
}

// Make it available globally for console testing
if (typeof window !== 'undefined') {
  (window as any).USBDeviceTest = USBDeviceTest
}

export default USBDeviceTest
