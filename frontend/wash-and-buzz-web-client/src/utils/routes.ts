import { Routes } from '@/types/module/commonModule'

export const appRoutes: Routes = {
  home: '/',
  homePage: '/home',
  login: '/login',
  signIn: '/signin',
  pocMainPage: '/poc-main-page',
  mqtt: '/mqtt',
  indexdb: '/indexdb',
  redux: '/redux',
  sse: '/sse',
  websocket: '/websocket',
  theme: '/theme',
  defaultState: '/default-state',
  arBilling: '/arBilling',
  pos: '/pos',
  assignRack: '/assign-rack',
  delivery: '/delivery',
  dashboard: '/dashboard',
  profile: '/profile',
  tickets: '/tickets',
  myTeam: '/settings/my-team',
  operations: '/settings/operations',
  generalDisplay: '/settings/operations/general-display',
  fulfillment: '/settings/operations/fulfillment',
  priceAdjustment: '/settings/operations/price-adjustment',
  tax: '/settings/operations/tax',
  ticketSettings: '/settings/operations/ticket-settings',
  reports: '/settings/reports',
  storeDetails: '/settings/store-details',
  itemLibrary: 'item-library',
  categories: '/settings/item-library/categories',
  itemsPriceList: '/settings/item-library/items-price-list',
  variants: '/settings/item-library/variants',
  permissions: '/settings/my-team/permissions',
  positions: '/settings/my-team/positions',
  teamMembers: '/settings/my-team/team-members',
  hardware: '/settings/hardware',
  printer: '/settings/hardware/printer',
  printerDiscovery: '/settings/hardware/printer-discovery',
  paymentTerminal: '/settings/hardware/payment-terminals',
  revenueSummary: '/settings/reports/revenue-summary',
  salesAnalysis: '/settings/reports/sales-analysis',
  itemTransactions: '/settings/reports/item-transactions',
  notFound: '/not-found',
  userNotFound: '/user-not-found',
  selectStoreData: '/select-store-data',
  selectPrinterData: '/select-printer-data',
  selectPaymentTerminalData: '/select-payment-terminal-data',
  iframeStyling: '/iframe-styling',
  stmEditor: '/stm-editor',
  barcodeScanner: '/barcode-scanner',
  storePickup: '/settings/operations/store-pickup',
  pickupDelivery: '/settings/operations/pickup-delivery',
}
