/**
 * Printer Discovery Error Handling
 * Comprehensive error handling and user feedback for printer discovery
 */

import { PrinterConnectionMethod } from '@/types/module/printersModule'

export enum PrinterDiscoveryErrorType {
  BROWSER_NOT_SUPPORTED = 'BROWSER_NOT_SUPPORTED',
  PERMISSION_DENIED = 'PERMISSION_DENIED',
  DEVICE_NOT_FOUND = 'DEVICE_NOT_FOUND',
  CONNECTION_FAILED = 'CONNECTION_FAILED',
  TIMEOUT = 'TIMEOUT',
  NETWORK_ERROR = 'NETWORK_ERROR',
  INVALID_CONFIGURATION = 'INVALID_CONFIGURATION',
  SERVICE_UNAVAILABLE = 'SERVICE_UNAVAILABLE',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR',
}

export interface PrinterDiscoveryError {
  type: PrinterDiscoveryErrorType
  method: PrinterConnectionMethod
  message: string
  userMessage: string
  troubleshooting: string[]
  canRetry: boolean
  retryDelay?: number
}

export class PrinterDiscoveryErrorHandler {
  /**
   * Create a standardized error from various error sources
   */
  static createError(
    error: any,
    method: PrinterConnectionMethod,
    context?: string
  ): PrinterDiscoveryError {
    // Handle specific error types
    if (error instanceof DOMException) {
      return this.handleDOMException(error, method)
    }

    if (error instanceof TypeError) {
      return this.handleTypeError(error, method)
    }

    if (error?.name) {
      switch (error.name) {
        case 'NotFoundError':
          return this.createNotFoundError(method)
        case 'NotAllowedError':
          return this.createPermissionError(method)
        case 'SecurityError':
          return this.createSecurityError(method)
        case 'NetworkError':
          return this.createNetworkError(method)
        case 'TimeoutError':
          return this.createTimeoutError(method)
      }
    }

    // Handle string errors
    if (typeof error === 'string') {
      if (error.toLowerCase().includes('not supported')) {
        return this.createBrowserNotSupportedError(method)
      }
      if (error.toLowerCase().includes('permission')) {
        return this.createPermissionError(method)
      }
      if (error.toLowerCase().includes('timeout')) {
        return this.createTimeoutError(method)
      }
    }

    // Default unknown error
    return this.createUnknownError(error, method, context)
  }

  /**
   * Handle DOM exceptions
   */
  private static handleDOMException(
    error: DOMException,
    method: PrinterConnectionMethod
  ): PrinterDiscoveryError {
    switch (error.name) {
      case 'NotFoundError':
        return this.createNotFoundError(method)
      case 'NotAllowedError':
        return this.createPermissionError(method)
      case 'SecurityError':
        return this.createSecurityError(method)
      case 'NetworkError':
        return this.createNetworkError(method)
      default:
        return this.createUnknownError(error, method)
    }
  }

  /**
   * Handle type errors
   */
  private static handleTypeError(
    error: TypeError,
    method: PrinterConnectionMethod
  ): PrinterDiscoveryError {
    if (error.message.includes('not supported') || error.message.includes('undefined')) {
      return this.createBrowserNotSupportedError(method)
    }
    return this.createUnknownError(error, method)
  }

  /**
   * Create browser not supported error
   */
  private static createBrowserNotSupportedError(
    method: PrinterConnectionMethod
  ): PrinterDiscoveryError {
    const methodName = this.getMethodDisplayName(method)
    
    return {
      type: PrinterDiscoveryErrorType.BROWSER_NOT_SUPPORTED,
      method,
      message: `${methodName} not supported in this browser`,
      userMessage: `Your browser doesn't support ${methodName} printer discovery`,
      troubleshooting: [
        `Use a modern browser like Chrome or Edge for ${methodName} support`,
        'Ensure you\'re using HTTPS (required for device APIs)',
        'Update your browser to the latest version',
        'Try a different connection method',
      ],
      canRetry: false,
    }
  }

  /**
   * Create permission denied error
   */
  private static createPermissionError(
    method: PrinterConnectionMethod
  ): PrinterDiscoveryError {
    const methodName = this.getMethodDisplayName(method)
    
    return {
      type: PrinterDiscoveryErrorType.PERMISSION_DENIED,
      method,
      message: `Permission denied for ${methodName} access`,
      userMessage: `Permission required to access ${methodName} devices`,
      troubleshooting: [
        'Click "Allow" when prompted for device access',
        'Check browser settings for device permissions',
        'Ensure the device is properly connected',
        'Try refreshing the page and attempting again',
      ],
      canRetry: true,
      retryDelay: 2000,
    }
  }

  /**
   * Create device not found error
   */
  private static createNotFoundError(
    method: PrinterConnectionMethod
  ): PrinterDiscoveryError {
    const methodName = this.getMethodDisplayName(method)
    
    return {
      type: PrinterDiscoveryErrorType.DEVICE_NOT_FOUND,
      method,
      message: `No ${methodName} devices found`,
      userMessage: `No ${methodName} printers were found`,
      troubleshooting: [
        'Ensure the printer is turned on and ready',
        'Check that the printer is properly connected',
        'Verify the printer is on the same network (for network printers)',
        'Try pairing the device first (for Bluetooth printers)',
        'Check printer compatibility with your system',
      ],
      canRetry: true,
      retryDelay: 3000,
    }
  }

  /**
   * Create security error
   */
  private static createSecurityError(
    method: PrinterConnectionMethod
  ): PrinterDiscoveryError {
    return {
      type: PrinterDiscoveryErrorType.PERMISSION_DENIED,
      method,
      message: 'Security restrictions prevent device access',
      userMessage: 'Security settings are blocking device access',
      troubleshooting: [
        'Ensure you\'re using HTTPS (required for device APIs)',
        'Check if the site is trusted in your browser',
        'Disable any ad blockers or security extensions temporarily',
        'Try using an incognito/private browsing window',
      ],
      canRetry: true,
      retryDelay: 2000,
    }
  }

  /**
   * Create network error
   */
  private static createNetworkError(
    method: PrinterConnectionMethod
  ): PrinterDiscoveryError {
    return {
      type: PrinterDiscoveryErrorType.NETWORK_ERROR,
      method,
      message: 'Network connection failed',
      userMessage: 'Unable to connect to the network or device',
      troubleshooting: [
        'Check your internet connection',
        'Ensure the printer is on the same network',
        'Verify network settings and firewall configuration',
        'Try restarting your router or network connection',
        'Check if the printer\'s IP address is accessible',
      ],
      canRetry: true,
      retryDelay: 5000,
    }
  }

  /**
   * Create timeout error
   */
  private static createTimeoutError(
    method: PrinterConnectionMethod
  ): PrinterDiscoveryError {
    const methodName = this.getMethodDisplayName(method)
    
    return {
      type: PrinterDiscoveryErrorType.TIMEOUT,
      method,
      message: `${methodName} discovery timed out`,
      userMessage: `Discovery took too long and was cancelled`,
      troubleshooting: [
        'The network may be slow or congested',
        'Try reducing the scan range for network discovery',
        'Ensure devices are powered on and responsive',
        'Check for network connectivity issues',
        'Try again with a longer timeout period',
      ],
      canRetry: true,
      retryDelay: 3000,
    }
  }

  /**
   * Create connection failed error
   */
  private static createConnectionError(
    method: PrinterConnectionMethod,
    details?: string
  ): PrinterDiscoveryError {
    const methodName = this.getMethodDisplayName(method)
    
    return {
      type: PrinterDiscoveryErrorType.CONNECTION_FAILED,
      method,
      message: `Failed to connect to ${methodName} device`,
      userMessage: `Unable to establish connection with the printer`,
      troubleshooting: [
        'Check that the printer is turned on',
        'Verify the connection cable or wireless connection',
        'Ensure the printer is not in use by another application',
        'Try restarting the printer',
        'Check printer drivers and software',
      ],
      canRetry: true,
      retryDelay: 3000,
    }
  }

  /**
   * Create unknown error
   */
  private static createUnknownError(
    error: any,
    method: PrinterConnectionMethod,
    context?: string
  ): PrinterDiscoveryError {
    const errorMessage = error?.message || error?.toString() || 'Unknown error occurred'
    
    return {
      type: PrinterDiscoveryErrorType.UNKNOWN_ERROR,
      method,
      message: errorMessage,
      userMessage: 'An unexpected error occurred during printer discovery',
      troubleshooting: [
        'Try refreshing the page and attempting again',
        'Check browser console for more details',
        'Ensure all devices are properly connected',
        'Try a different connection method',
        'Contact support if the problem persists',
      ],
      canRetry: true,
      retryDelay: 2000,
    }
  }

  /**
   * Get display name for connection method
   */
  private static getMethodDisplayName(method: PrinterConnectionMethod): string {
    switch (method) {
      case PrinterConnectionMethod.USB:
        return 'USB'
      case PrinterConnectionMethod.BLUETOOTH:
        return 'Bluetooth'
      case PrinterConnectionMethod.NETWORK:
        return 'Network'
      case PrinterConnectionMethod.ETHERNET:
        return 'Ethernet'
      case PrinterConnectionMethod.MANUAL:
        return 'Manual'
      default:
        return 'Device'
    }
  }

  /**
   * Get user-friendly error message with troubleshooting
   */
  static getErrorMessage(error: PrinterDiscoveryError): string {
    let message = error.userMessage
    
    if (error.troubleshooting.length > 0) {
      message += '\n\nTroubleshooting tips:\n'
      message += error.troubleshooting.map(tip => `• ${tip}`).join('\n')
    }
    
    if (error.canRetry) {
      message += '\n\nYou can try again after addressing these issues.'
    }
    
    return message
  }

  /**
   * Check if error is recoverable
   */
  static isRecoverable(error: PrinterDiscoveryError): boolean {
    return error.canRetry && error.type !== PrinterDiscoveryErrorType.BROWSER_NOT_SUPPORTED
  }

  /**
   * Get retry delay for error
   */
  static getRetryDelay(error: PrinterDiscoveryError): number {
    return error.retryDelay || 2000
  }
}

export default PrinterDiscoveryErrorHandler
