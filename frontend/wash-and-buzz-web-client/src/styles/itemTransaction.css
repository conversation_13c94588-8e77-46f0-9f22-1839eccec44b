.item-transaction-table-wrapper {
  border-radius: 8px;
  height: calc(100vh - 305px) !important;
  background-color: #fff !important;
}

.item-transaction-wrapper {
  border-radius: 8px;
}

.item-transaction-table-body-wrapper {
  overflow-y: auto;
}

.item-transaction-table thead {
  border-bottom: 1px solid #a6a6a6;
  position: sticky;
  top: 0;
  background-color: #fff !important;
}

.item-transaction-table thead th:first-child {
  text-align: left;
}

.item-transaction-table thead th {
  text-align: end;
  font-family: Poppins;
  font-weight: 600;
}

.item-transaction-table tbody {
  background-color: transparent;
}

.item-transaction-table tbody tr {
  border-bottom: 1px solid #eaeaea;
}

.item-transaction-table td:first-child,
.item-transaction-table thead tr th:first-child {
  border-right: 1px solid #eaeaea;
}

.item-transaction-table tbody td:first-child {
  text-align: left;
}

.item-transaction-table tbody td {
  padding: 10px;
  text-align: end;
}

.item-transaction-table {
  width: 100%;
  height: 100%;
  display: block;
  overflow-y: auto;
}

.item-transaction-table thead {
  display: table;
  width: 100%;
  table-layout: fixed;
  border-bottom: 1px solid #a6a6a6;
  background-color: #fff !important;
  position: sticky;
  top: 0;
  z-index: 10;
}

.item-transaction-table thead tr {
  display: table-row;
}

.item-transaction-table thead th:first-child {
  text-align: left;
}

.item-transaction-table thead th {
  display: table-cell;
  text-align: end;
  font-family: Poppins;
  font-weight: 600;
  padding: 10px;
  vertical-align: middle;
}

.item-transaction-table-body-wrapper {
  display: block;
  height: calc(100% - 60px);
  overflow-y: auto;
  width: 100%;
  scrollbar-gutter: stable;
}

.item-transaction-table tbody {
  display: table;
  width: 100%;
  table-layout: fixed;
  background-color: transparent;
}

.item-transaction-table tbody tr {
  display: flex;
  border-bottom: 1px solid #eaeaea;
}

.item-transaction-table td:first-child,
.item-transaction-table thead tr th:first-child {
  border-right: 1px solid #eaeaea;
}

.item-transaction-table tbody td:first-child {
  text-align: left;
}

.item-transaction-table tbody td {
  display: table-cell;
  padding: 20px 10px;
  text-align: end;
  vertical-align: middle;
  /*  */
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  max-width: 30ch;
}

/* Ensure columns have equal width distribution */
.item-transaction-table thead th,
.item-transaction-table tbody td {
  width: 16.66%; /* 6 columns = 100%/6 */
}

.item-transaction-table thead th:first-child,
.item-transaction-table tbody td:first-child {
  width: 20%;
}

/* Custom scrollbar styling */
.item-transaction-table-body-wrapper::-webkit-scrollbar {
  width: 6px;
}

.item-transaction-table-body-wrapper::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.item-transaction-table-body-wrapper::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.item-transaction-table-body-wrapper::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Fix for empty state message */
.item-transaction-table-body-wrapper .text-center {
  display: table-cell;
  text-align: center;
  vertical-align: middle;
  height: 200px;
  width: 100%;
}
